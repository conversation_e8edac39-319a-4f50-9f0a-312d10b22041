import type React from "react"
import type { Metada<PERSON> } from "next"
import "./globals.css"
import "@/styles/typography.css"
import "@/styles/premium-typography.css"
import "@/styles/search-utilities.css"
import Header from "@/components/header"
import Footer from "@/components/footer"
import { ThemeProvider } from "@/components/theme-provider"
import { LenisProvider } from "@/providers/lenis-provider"
import { AuthProvider } from "@/contexts/auth-context"
import { CookieConsentProvider } from "@/contexts/cookie-consent-context"
import { SecureQuotationProvider } from "@/hooks/use-secure-quotation"
import { Toaster } from "@/components/ui/sonner"
import CookieConsentBanner from "@/components/cookie-consent-banner"
import { AnalyticsProvider } from "@/components/analytics-provider"
import { ScrollToTop } from "@/components/ui/scroll-to-top"
import "@/lib/cookie-cleanup" // Import cleanup utility early

// Import font configurations
import {
  sfPro,
  inter,
  poppins,
  montserrat,
  sourceSansPro,
  dmSans,
  spaceGrotesk,
  fontFamilies
} from '@/styles/fonts'

export const metadata: Metadata = {
  title: "Benzochem Industries | Premium Chemical Products",
  description:
    "Specialized chemical trading company offering high-quality powder and liquid products for industrial applications.",
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <script src="/cookie-cleanup.js" />
      </head>
      <body
        className={`
          ${sfPro.variable}
          ${inter.variable}
          ${poppins.variable}
          ${montserrat.variable}
          ${sourceSansPro.variable}
          ${dmSans.variable}
          ${spaceGrotesk.variable}
          font-sans antialiased
        `}
        style={{
          '--font-headings': fontFamilies.headings,
          '--font-body': fontFamilies.body,
          '--font-ui': fontFamilies.ui,
        } as React.CSSProperties}
      >
        <LenisProvider>
          <ThemeProvider attribute="class" defaultTheme="light" enableSystem disableTransitionOnChange>
            <CookieConsentProvider>
              <AuthProvider>
                <SecureQuotationProvider>
                  <AnalyticsProvider>
                    <Header />
                    {children}
                    <Footer />
                    <CookieConsentBanner />
                    <Toaster />
                    <ScrollToTop />
                  </AnalyticsProvider>
                </SecureQuotationProvider>
              </AuthProvider>
            </CookieConsentProvider>
          </ThemeProvider>
        </LenisProvider>
      </body>
    </html>
  )
}