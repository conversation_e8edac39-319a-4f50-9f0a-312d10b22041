# =============================================================================
# ADMIN API CONFIGURATION
# =============================================================================
# URL of the admin backend API (where the admin project is running)
NEXT_PUBLIC_ADMIN_API_URL=https://apibenzochem.vercel.app/

# API key for accessing the admin backend
# Get this from the admin dashboard after creating an API key
# IMPORTANT: Replace with your actual API key from the admin project
# This API key needs the following permissions:
# - users:read, users:write
# - products:read
# - collections:read
# - quotations:read, quotations:write
NEXT_PUBLIC_API_KEY=bzk_live_KNkDFFq7YXizJpStewzShNwVD20loWZd

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
# Authentication URLs (Admin API-based)
NEXT_PUBLIC_SIGN_IN_URL=/login
NEXT_PUBLIC_SIGN_UP_URL=/register
NEXT_PUBLIC_AFTER_SIGN_IN_URL=/account
NEXT_PUBLIC_AFTER_SIGN_UP_URL=/account

# =============================================================================
# RAPIDAPI GST VERIFICATION SERVICE
# =============================================================================
# RapidAPI GST Verification API Credentials
RAPIDAPI_KEY=**************************************************
RAPIDAPI_GST_HOST=gst-return-status.p.rapidapi.com
RAPIDAPI_GST_BASE_URL=https://gst-return-status.p.rapidapi.com/free

# =============================================================================
# GOOGLE PLACES API CONFIGURATION
# =============================================================================
# Google Places API Configuration
GOOGLE_PLACES_API_KEY=AIzaSyAVipoBOjQ5fefw-V92HOhBBn7Zn9quoYk

# =============================================================================
# LEGACY SHOPIFY CONFIGURATION (DEPRECATED)
# =============================================================================
# These are kept for reference but are no longer used
# The application now uses the admin API instead of Shopify
# NEXT_PUBLIC_SHOPIFY_STORE_DOMAIN=benzochem.myshopify.com
# NEXT_PUBLIC_SHOPIFY_STOREFRONT_ACCESS_TOKEN=95029391ce9ba8bad8a604e7faf7eb87
# SHOPIFY_STORE_DOMAIN=benzochem.myshopify.com
# SHOPIFY_ADMIN_API_ACCESS_TOKEN=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
# SHOPIFY_API_VERSION=2025-04
# SHOPIFY_WEBHOOK_SECRET=4db46df9e2ec1150d570f2963416714f93aabf91185556b0686c8b507eee5573

# =============================================================================
# SETUP INSTRUCTIONS
# =============================================================================
# 1. Start the admin project first (usually on port 3001)
# 2. Create an API key in the admin dashboard with these permissions:
#    - users:read, users:write
#    - products:read
#    - collections:read
#    - quotations:read, quotations:write
# 3. Replace "your_api_key_here" with the actual API key
# 4. Update NEXT_PUBLIC_ADMIN_API_URL if admin runs on different port/domain
# 5. Start the user project (usually on port 3000)