{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Benzochem%20Industries%20v2/edit-13%20%28%20clone%20%29/Benzochem-Industries/user/components/product-detail.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/product-detail.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/product-detail.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiS,GAC9T,+DACA", "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Benzochem%20Industries%20v2/edit-13%20%28%20clone%20%29/Benzochem-Industries/user/components/product-detail.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/product-detail.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/product-detail.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6Q,GAC1S,2CACA", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Benzochem%20Industries%20v2/edit-13%20%28%20clone%20%29/Benzochem-Industries/user/components/product-specifications.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/product-specifications.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/product-specifications.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyS,GACtU,uEACA", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Benzochem%20Industries%20v2/edit-13%20%28%20clone%20%29/Benzochem-Industries/user/components/product-specifications.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/product-specifications.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/product-specifications.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqR,GAClT,mDACA", "debugId": null}}, {"offset": {"line": 96, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Benzochem%20Industries%20v2/edit-13%20%28%20clone%20%29/Benzochem-Industries/user/components/related-products.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/related-products.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/related-products.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmS,GAChU,iEACA", "debugId": null}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Benzochem%20Industries%20v2/edit-13%20%28%20clone%20%29/Benzochem-Industries/user/components/related-products.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/related-products.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/related-products.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+Q,GAC5S,6CACA", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 144, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Benzochem%20Industries%20v2/edit-13%20%28%20clone%20%29/Benzochem-Industries/user/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\n\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"skeleton\"\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Skeleton }\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Benzochem%20Industries%20v2/edit-13%20%28%20clone%20%29/Benzochem-Industries/user/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Benzochem%20Industries%20v2/edit-13%20%28%20clone%20%29/Benzochem-Industries/user/lib/api-client.ts"], "sourcesContent": ["// Import the Product type from types.ts to maintain consistency\nimport type { Product } from './types';\n\n// API client for communicating with the admin backend\nclass ApiClient {\n  private baseUrl: string;\n  private apiKey: string;\n\n  constructor() {\n    this.baseUrl = process.env.NEXT_PUBLIC_ADMIN_API_URL || 'http://localhost:3001';\n    this.apiKey = process.env.NEXT_PUBLIC_API_KEY || '';\n    \n    // Validate configuration\n    if (!this.apiKey || this.apiKey === 'your_api_key_here') {\n      console.warn('⚠️ API key not configured. Please set NEXT_PUBLIC_API_KEY in .env.local');\n    }\n  }\n\n  private async makeRequest<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<{ success: boolean; data?: T; error?: string; pagination?: any }> {\n    try {\n      // Check if API key is configured\n      if (!this.apiKey || this.apiKey === 'your_api_key_here') {\n        return {\n          success: false,\n          error: 'API key not configured. Please set NEXT_PUBLIC_API_KEY in .env.local',\n        };\n      }\n\n      const url = `${this.baseUrl}/api/v1${endpoint}`;\n      \n      const response = await fetch(url, {\n        ...options,\n        headers: {\n          'Content-Type': 'application/json',\n          'X-API-Key': this.apiKey,\n          ...options.headers,\n        },\n      });\n\n      // Check if response is ok first\n      if (!response.ok) {\n        let errorMessage = `HTTP ${response.status}`;\n        try {\n          const errorResult = await response.json();\n          errorMessage = errorResult.error || errorMessage;\n        } catch {\n          // If JSON parsing fails, use status text\n          errorMessage = response.statusText || errorMessage;\n        }\n        throw new Error(errorMessage);\n      }\n\n      // Try to parse JSON response\n      let result;\n      try {\n        const responseText = await response.text();\n        if (!responseText.trim()) {\n          throw new Error('Empty response from server');\n        }\n        \n        // Try to fix common JSON formatting issues\n        let fixedJson = responseText;\n        \n        // Fix missing commas between JSON properties (common issue we're seeing)\n        fixedJson = fixedJson.replace(/(\":\\s*(?:true|false|null|\\d+|\"[^\"]*\"))\\s*(\")/g, '$1,$2');\n        fixedJson = fixedJson.replace(/(\":\\s*\\{[^}]*\\})\\s*(\")/g, '$1,$2');\n        fixedJson = fixedJson.replace(/(\":\\s*\\[[^\\]]*\\])\\s*(\")/g, '$1,$2');\n        \n        try {\n          result = JSON.parse(fixedJson);\n        } catch (secondParseError) {\n          // If fixing didn't work, try the original\n          result = JSON.parse(responseText);\n        }\n      } catch (parseError) {\n        console.error('JSON parse error:', parseError);\n        console.error('Response text preview:', responseText?.substring(0, 200) + '...');\n        throw new Error('Invalid JSON response from server');\n      }\n\n      return result;\n    } catch (error) {\n      console.error(`API request failed for ${endpoint}:`, error);\n      \n      // Provide more helpful error messages\n      let errorMessage = 'Unknown error';\n      if (error instanceof Error) {\n        if (error.message.includes('Failed to fetch') || error.name === 'TypeError') {\n          errorMessage = 'Cannot connect to admin API. Make sure admin project is running on port 3001';\n        } else if (error.message.includes('JSON')) {\n          errorMessage = 'Server returned invalid response format';\n        } else {\n          errorMessage = error.message;\n        }\n      }\n      \n      return {\n        success: false,\n        error: errorMessage,\n      };\n    }\n  }\n\n  // Products API\n  async getProducts(params: {\n    limit?: number;\n    offset?: number;\n    search?: string;\n    collection?: string;\n    featured?: boolean;\n  } = {}) {\n    const searchParams = new URLSearchParams();\n    \n    if (params.limit) searchParams.set('limit', params.limit.toString());\n    if (params.offset) searchParams.set('offset', params.offset.toString());\n    if (params.search) searchParams.set('search', params.search);\n    if (params.collection) searchParams.set('collection', params.collection);\n    if (params.featured) searchParams.set('featured', 'true');\n    \n    const query = searchParams.toString();\n    const endpoint = `/products${query ? `?${query}` : ''}`;\n    \n    return this.makeRequest(endpoint);\n  }\n\n  async getProductById(productId: string) {\n    return this.makeRequest(`/products/${productId}`);\n  }\n\n  // Collections API\n  async getCollections(params: {\n    limit?: number;\n    offset?: number;\n    search?: string;\n    visible?: boolean;\n  } = {}) {\n    const searchParams = new URLSearchParams();\n    \n    if (params.limit) searchParams.set('limit', params.limit.toString());\n    if (params.offset) searchParams.set('offset', params.offset.toString());\n    if (params.search) searchParams.set('search', params.search);\n    if (params.visible !== undefined) searchParams.set('visible', params.visible.toString());\n    \n    const query = searchParams.toString();\n    const endpoint = `/collections${query ? `?${query}` : ''}`;\n    \n    return this.makeRequest(endpoint);\n  }\n\n  async getCollectionById(collectionId: string) {\n    return this.makeRequest(`/collections/${collectionId}`);\n  }\n\n  // Users API (we'll need to create this endpoint in admin)\n  async createUser(userData: {\n    userId: string;\n    email: string;\n    firstName: string;\n    lastName: string;\n    phone?: string;\n    businessName?: string;\n    gstNumber?: string;\n    legalNameOfBusiness?: string;\n    tradeName?: string;\n    dateOfRegistration?: string;\n    constitutionOfBusiness?: string;\n    taxpayerType?: string;\n    principalPlaceOfBusiness?: string;\n    natureOfCoreBusinessActivity?: string;\n    gstStatus?: string;\n    agreedToEmailMarketing?: boolean;\n    agreedToSmsMarketing?: boolean;\n  }) {\n    return this.makeRequest('/users', {\n      method: 'POST',\n      body: JSON.stringify(userData),\n    });\n  }\n\n  async getUserByEmail(email: string) {\n    return this.makeRequest(`/users?email=${encodeURIComponent(email)}`);\n  }\n\n  async updateUserLogin(userId: string) {\n    return this.makeRequest(`/users/${userId}/login`, {\n      method: 'PATCH',\n    });\n  }\n\n  // Quotations API (we'll need to create this endpoint in admin)\n  async createQuotation(quotationData: {\n    userId: string;\n    userEmail: string;\n    userName: string;\n    userPhone?: string;\n    businessName?: string;\n    products: Array<{\n      productId: string;\n      productName: string;\n      quantity: string;\n      unit: string;\n      specifications?: string;\n    }>;\n    additionalRequirements?: string;\n    deliveryLocation?: string;\n    urgency?: 'standard' | 'urgent' | 'asap';\n  }) {\n    return this.makeRequest('/quotations', {\n      method: 'POST',\n      body: JSON.stringify(quotationData),\n    });\n  }\n\n  async getUserQuotations(userId: string, params: {\n    limit?: number;\n    offset?: number;\n    status?: string;\n  } = {}) {\n    const searchParams = new URLSearchParams();\n    searchParams.set('userId', userId);\n    \n    if (params.limit) searchParams.set('limit', params.limit.toString());\n    if (params.offset) searchParams.set('offset', params.offset.toString());\n    if (params.status) searchParams.set('status', params.status);\n    \n    const query = searchParams.toString();\n    return this.makeRequest(`/quotations?${query}`);\n  }\n\n  async getQuotation(quotationId: string) {\n    return this.makeRequest(`/quotations/${quotationId}`);\n  }\n\n  async updateQuotationStatus(quotationId: string, data: {\n    status: string;\n    adminResponse?: {\n      quotedBy?: string;\n      totalAmount?: string;\n      validUntil?: string;\n      terms?: string;\n      notes?: string;\n    };\n  }) {\n    return this.makeRequest(`/quotations/${quotationId}`, {\n      method: 'PATCH',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async acceptQuotation(quotationId: string) {\n    return this.updateQuotationStatus(quotationId, { status: 'accepted' });\n  }\n\n  async rejectQuotation(quotationId: string, reason?: string) {\n    return this.updateQuotationStatus(quotationId, { \n      status: 'rejected',\n      adminResponse: {\n        notes: reason\n      }\n    });\n  }\n\n  // GST Verification (production implementation)\n  async verifyGST(gstNumber: string): Promise<{ success: boolean; error?: string; data?: any }> {\n    // Simple validation pattern for Indian GST numbers\n    const gstPattern = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;\n\n    if (!gstPattern.test(gstNumber)) {\n      return {\n        success: false,\n        error: \"Invalid GST number format\",\n      };\n    }\n\n    try {\n      // TODO: Implement real GST verification API\n      // This should connect to actual GST verification service\n      // For now, return basic validation\n      return {\n        success: true,\n        data: {\n          valid: true,\n          businessName: \"Trade Name (To be fetched from GST API)\",\n          address: \"Business Address (To be fetched from GST API)\",\n        }\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: \"GST verification service unavailable\",\n      };\n    }\n  }\n}\n\n// Export singleton instance\nexport const apiClient = new ApiClient();\n\n// Transform API product data to match the expected Product interface\nexport function transformApiProductToProduct(apiProduct: any): Product {\n  return {\n    id: apiProduct.id,\n    title: apiProduct.title,\n    description: apiProduct.description,\n    descriptionHtml: apiProduct.description, // Use description as HTML for now\n    tags: apiProduct.tags || [],\n    quantity: apiProduct.quantity, // Include the quantity field from admin\n    collections: {\n      edges: (apiProduct.collections || []).map((collection: string) => ({\n        node: { title: collection }\n      }))\n    },\n    images: {\n      edges: (apiProduct.images || []).map((image: any) => ({\n        node: { url: image.url || image }\n      }))\n    },\n    media: {\n      edges: (apiProduct.images || []).map((image: any, index: number) => ({\n        node: {\n          id: `media_${index}`,\n          image: { url: image.url || image }\n        }\n      }))\n    },\n    priceRange: {\n      minVariantPrice: {\n        amount: apiProduct.priceRange?.minVariantPrice?.amount || '0.00',\n        currencyCode: apiProduct.priceRange?.minVariantPrice?.currencyCode || 'USD'\n      },\n      maxVariantPrice: {\n        amount: apiProduct.priceRange?.maxVariantPrice?.amount || apiProduct.priceRange?.minVariantPrice?.amount || '0.00',\n        currencyCode: apiProduct.priceRange?.maxVariantPrice?.currencyCode || apiProduct.priceRange?.minVariantPrice?.currencyCode || 'USD'\n      }\n    },\n    compareAtPriceRange: {\n      minVariantPrice: {\n        amount: '0.00',\n        currencyCode: 'USD'\n      },\n      maxVariantPrice: {\n        amount: '0.00',\n        currencyCode: 'USD'\n      }\n    },\n    metafields: [\n      // Transform chemical-specific fields to metafields\n      ...(apiProduct.purity ? [{\n        id: 'purity',\n        key: 'purity',\n        namespace: 'chemical',\n        value: apiProduct.purity,\n        type: 'single_line_text_field'\n      }] : []),\n      ...(apiProduct.casNumber ? [{\n        id: 'cas_number',\n        key: 'cas_number',\n        namespace: 'chemical',\n        value: apiProduct.casNumber,\n        type: 'single_line_text_field'\n      }] : []),\n      ...(apiProduct.molecularFormula ? [{\n        id: 'molecular_formula',\n        key: 'molecular_formula',\n        namespace: 'chemical',\n        value: apiProduct.molecularFormula,\n        type: 'single_line_text_field'\n      }] : []),\n      ...(apiProduct.molecularWeight ? [{\n        id: 'molecular_weight',\n        key: 'molecular_weight',\n        namespace: 'chemical',\n        value: apiProduct.molecularWeight,\n        type: 'single_line_text_field'\n      }] : []),\n      ...(apiProduct.appearance ? [{\n        id: 'appearance',\n        key: 'appearance',\n        namespace: 'chemical',\n        value: apiProduct.appearance,\n        type: 'single_line_text_field'\n      }] : []),\n      ...(apiProduct.solubility ? [{\n        id: 'solubility',\n        key: 'solubility',\n        namespace: 'chemical',\n        value: apiProduct.solubility,\n        type: 'single_line_text_field'\n      }] : []),\n      ...(apiProduct.phValue ? [{\n        id: 'ph_value',\n        key: 'ph_value',\n        namespace: 'chemical',\n        value: apiProduct.phValue,\n        type: 'single_line_text_field'\n      }] : []),\n      ...(apiProduct.hsnNumber ? [{\n        id: 'hsn_number',\n        key: 'hsn_number',\n        namespace: 'chemical',\n        value: apiProduct.hsnNumber,\n        type: 'single_line_text_field'\n      }] : []),\n      ...(apiProduct.chemicalName ? [{\n        id: 'chemical_name',\n        key: 'chemical_name',\n        namespace: 'chemical',\n        value: apiProduct.chemicalName,\n        type: 'single_line_text_field'\n      }] : []),\n      ...(apiProduct.packaging ? [{\n        id: 'packaging',\n        key: 'packaging',\n        namespace: 'chemical',\n        value: JSON.stringify(apiProduct.packaging),\n        type: 'json'\n      }] : []),\n      ...(apiProduct.features ? [{\n        id: 'features',\n        key: 'features',\n        namespace: 'chemical',\n        value: JSON.stringify(apiProduct.features),\n        type: 'json'\n      }] : []),\n      ...(apiProduct.applications ? [{\n        id: 'applications',\n        key: 'applications',\n        namespace: 'chemical',\n        value: JSON.stringify(apiProduct.applications),\n        type: 'json'\n      }] : []),\n      ...(apiProduct.applicationDetails ? [{\n        id: 'application_details',\n        key: 'application_details',\n        namespace: 'chemical',\n        value: JSON.stringify(apiProduct.applicationDetails),\n        type: 'json'\n      }] : [])\n    ]\n  }\n}\n\n// Export convenience functions that match the api.ts interface\nexport async function getFeaturedProducts(): Promise<Product[]> {\n  try {\n    const response = await apiClient.getProducts({ featured: true, limit: 8 });\n    if (response.success && response.data && Array.isArray(response.data)) {\n      return (response.data as ApiProduct[]).map(transformApiProductToProduct);\n    }\n    \n    // If API fails, log error and return empty array\n    if (!response.success) {\n      console.warn('API request failed:', response.error);\n    }\n    \n    return [];\n  } catch (error) {\n    console.error('Error fetching featured products:', error);\n    return [];\n  }\n}\n\nexport async function searchProducts(query: string): Promise<Product[]> {\n  try {\n    const response = await apiClient.getProducts({ search: query, limit: 20 });\n    if (response.success && response.data && Array.isArray(response.data)) {\n      return (response.data as ApiProduct[]).map(transformApiProductToProduct);\n    }\n    return [];\n  } catch (error) {\n    console.error('Error searching products:', error);\n    return [];\n  }\n}\n\nexport async function getProductsByCollection(collectionTitle: string): Promise<Product[]> {\n  try {\n    console.log(`Fetching products for collection: \"${collectionTitle}\"`);\n    const response = await apiClient.getProducts({ collection: collectionTitle, limit: 100 });\n    console.log(`API response for collection \"${collectionTitle}\":`, {\n      success: response.success,\n      dataLength: response.data ? (Array.isArray(response.data) ? response.data.length : 'not array') : 'no data',\n      error: response.error\n    });\n    \n    if (response.success && response.data && Array.isArray(response.data)) {\n      const products = (response.data as ApiProduct[]).map(transformApiProductToProduct);\n      console.log(`Transformed ${products.length} products for collection \"${collectionTitle}\"`);\n      return products;\n    }\n    \n    console.warn(`No products found for collection \"${collectionTitle}\"`);\n    return [];\n  } catch (error) {\n    console.error(`Error fetching products for collection ${collectionTitle}:`, error);\n    return [];\n  }\n}\n\nexport async function getProductById(id: string): Promise<Product | null> {\n  try {\n    const response = await apiClient.getProductById(id);\n    if (response.success && response.data) {\n      return transformApiProductToProduct(response.data as ApiProduct);\n    }\n    return null;\n  } catch (error) {\n    console.error(`Error fetching product ${id}:`, error);\n    return null;\n  }\n}\n\n// Export types for better TypeScript support\nexport interface ApiProduct {\n  id: string;\n  title: string;\n  description: string;\n  tags: string[];\n  quantity?: number; // Available quantity for sale\n  collections: string[]; // Now contains collection titles, not IDs\n  images: Array<{\n    url: string;\n    altText?: string;\n  }>;\n  priceRange: {\n    minVariantPrice: {\n      amount: string;\n      currencyCode: string;\n    };\n    maxVariantPrice: {\n      amount: string;\n      currencyCode: string;\n    };\n  };\n  // Chemical-specific fields\n  purity?: string;\n  packaging?: string;\n  casNumber?: string;\n  hsnNumber?: string;\n  molecularFormula?: string;\n  molecularWeight?: string;\n  appearance?: string;\n  solubility?: string;\n  phValue?: string;\n  chemicalName?: string;\n  features?: string[];\n  applications?: string[];\n  applicationDetails?: string[];\n  status: string;\n  featured: boolean;\n  totalInventory?: number;\n  createdAt: number;\n  updatedAt: number;\n}\n\nexport interface Collection {\n  id: string;\n  title: string;\n  description?: string;\n  handle: string;\n  image?: {\n    url: string;\n    altText?: string;\n  };\n  seoTitle?: string;\n  seoDescription?: string;\n  status: string;\n  sortOrder?: number;\n  isVisible: boolean;\n  productCount?: number;\n  createdAt: number;\n  updatedAt: number;\n}\n\nexport interface Quotation {\n  _id: string;\n  userId: string;\n  userEmail: string;\n  userName: string;\n  userPhone?: string;\n  businessName?: string;\n  products: Array<{\n    productId: string;\n    productName: string;\n    quantity: string;\n    unit: string;\n    specifications?: string;\n  }>;\n  additionalRequirements?: string;\n  deliveryLocation?: string;\n  urgency: 'standard' | 'urgent' | 'asap';\n  status: 'pending' | 'processing' | 'quoted' | 'accepted' | 'rejected' | 'expired';\n  adminResponse?: {\n    quotedBy: string;\n    quotedAt: number;\n    totalAmount?: string;\n    validUntil?: number;\n    terms?: string;\n    notes?: string;\n  };\n  createdAt: number;\n  updatedAt: number;\n}"], "names": [], "mappings": "AAAA,gEAAgE;;;;;;;;;AAGhE,sDAAsD;AACtD,MAAM;IACI,QAAgB;IAChB,OAAe;IAEvB,aAAc;QACZ,IAAI,CAAC,OAAO,GAAG,wEAAyC;QACxD,IAAI,CAAC,MAAM,GAAG,iFAAmC;QAEjD,yBAAyB;QACzB,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,KAAK,qBAAqB;YACvD,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,MAAc,YACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACkD;QAC3E,IAAI;YACF,iCAAiC;YACjC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,KAAK,qBAAqB;gBACvD,OAAO;oBACL,SAAS;oBACT,OAAO;gBACT;YACF;YAEA,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,UAAU;YAE/C,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC,GAAG,OAAO;gBACV,SAAS;oBACP,gBAAgB;oBAChB,aAAa,IAAI,CAAC,MAAM;oBACxB,GAAG,QAAQ,OAAO;gBACpB;YACF;YAEA,gCAAgC;YAChC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,IAAI,eAAe,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE;gBAC5C,IAAI;oBACF,MAAM,cAAc,MAAM,SAAS,IAAI;oBACvC,eAAe,YAAY,KAAK,IAAI;gBACtC,EAAE,OAAM;oBACN,yCAAyC;oBACzC,eAAe,SAAS,UAAU,IAAI;gBACxC;gBACA,MAAM,IAAI,MAAM;YAClB;YAEA,6BAA6B;YAC7B,IAAI;YACJ,IAAI;gBACF,MAAM,gBAAe,MAAM,SAAS,IAAI;gBACxC,IAAI,CAAC,cAAa,IAAI,IAAI;oBACxB,MAAM,IAAI,MAAM;gBAClB;gBAEA,2CAA2C;gBAC3C,IAAI,YAAY;gBAEhB,yEAAyE;gBACzE,YAAY,UAAU,OAAO,CAAC,iDAAiD;gBAC/E,YAAY,UAAU,OAAO,CAAC,2BAA2B;gBACzD,YAAY,UAAU,OAAO,CAAC,4BAA4B;gBAE1D,IAAI;oBACF,SAAS,KAAK,KAAK,CAAC;gBACtB,EAAE,OAAO,kBAAkB;oBACzB,0CAA0C;oBAC1C,SAAS,KAAK,KAAK,CAAC;gBACtB;YACF,EAAE,OAAO,YAAY;gBACnB,QAAQ,KAAK,CAAC,qBAAqB;gBACnC,QAAQ,KAAK,CAAC,0BAA0B,cAAc,UAAU,GAAG,OAAO;gBAC1E,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,SAAS,CAAC,CAAC,EAAE;YAErD,sCAAsC;YACtC,IAAI,eAAe;YACnB,IAAI,iBAAiB,OAAO;gBAC1B,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,sBAAsB,MAAM,IAAI,KAAK,aAAa;oBAC3E,eAAe;gBACjB,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,SAAS;oBACzC,eAAe;gBACjB,OAAO;oBACL,eAAe,MAAM,OAAO;gBAC9B;YACF;YAEA,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;IACF;IAEA,eAAe;IACf,MAAM,YAAY,SAMd,CAAC,CAAC,EAAE;QACN,MAAM,eAAe,IAAI;QAEzB,IAAI,OAAO,KAAK,EAAE,aAAa,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACjE,IAAI,OAAO,MAAM,EAAE,aAAa,GAAG,CAAC,UAAU,OAAO,MAAM,CAAC,QAAQ;QACpE,IAAI,OAAO,MAAM,EAAE,aAAa,GAAG,CAAC,UAAU,OAAO,MAAM;QAC3D,IAAI,OAAO,UAAU,EAAE,aAAa,GAAG,CAAC,cAAc,OAAO,UAAU;QACvE,IAAI,OAAO,QAAQ,EAAE,aAAa,GAAG,CAAC,YAAY;QAElD,MAAM,QAAQ,aAAa,QAAQ;QACnC,MAAM,WAAW,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;QAEvD,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B;IAEA,MAAM,eAAe,SAAiB,EAAE;QACtC,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,UAAU,EAAE,WAAW;IAClD;IAEA,kBAAkB;IAClB,MAAM,eAAe,SAKjB,CAAC,CAAC,EAAE;QACN,MAAM,eAAe,IAAI;QAEzB,IAAI,OAAO,KAAK,EAAE,aAAa,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACjE,IAAI,OAAO,MAAM,EAAE,aAAa,GAAG,CAAC,UAAU,OAAO,MAAM,CAAC,QAAQ;QACpE,IAAI,OAAO,MAAM,EAAE,aAAa,GAAG,CAAC,UAAU,OAAO,MAAM;QAC3D,IAAI,OAAO,OAAO,KAAK,WAAW,aAAa,GAAG,CAAC,WAAW,OAAO,OAAO,CAAC,QAAQ;QAErF,MAAM,QAAQ,aAAa,QAAQ;QACnC,MAAM,WAAW,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;QAE1D,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B;IAEA,MAAM,kBAAkB,YAAoB,EAAE;QAC5C,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,aAAa,EAAE,cAAc;IACxD;IAEA,0DAA0D;IAC1D,MAAM,WAAW,QAkBhB,EAAE;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU;YAChC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,eAAe,KAAa,EAAE;QAClC,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,aAAa,EAAE,mBAAmB,QAAQ;IACrE;IAEA,MAAM,gBAAgB,MAAc,EAAE;QACpC,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,EAAE,OAAO,MAAM,CAAC,EAAE;YAChD,QAAQ;QACV;IACF;IAEA,+DAA+D;IAC/D,MAAM,gBAAgB,aAgBrB,EAAE;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,eAAe;YACrC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,kBAAkB,MAAc,EAAE,SAIpC,CAAC,CAAC,EAAE;QACN,MAAM,eAAe,IAAI;QACzB,aAAa,GAAG,CAAC,UAAU;QAE3B,IAAI,OAAO,KAAK,EAAE,aAAa,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACjE,IAAI,OAAO,MAAM,EAAE,aAAa,GAAG,CAAC,UAAU,OAAO,MAAM,CAAC,QAAQ;QACpE,IAAI,OAAO,MAAM,EAAE,aAAa,GAAG,CAAC,UAAU,OAAO,MAAM;QAE3D,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,YAAY,EAAE,OAAO;IAChD;IAEA,MAAM,aAAa,WAAmB,EAAE;QACtC,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,YAAY,EAAE,aAAa;IACtD;IAEA,MAAM,sBAAsB,WAAmB,EAAE,IAShD,EAAE;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE;YACpD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,gBAAgB,WAAmB,EAAE;QACzC,OAAO,IAAI,CAAC,qBAAqB,CAAC,aAAa;YAAE,QAAQ;QAAW;IACtE;IAEA,MAAM,gBAAgB,WAAmB,EAAE,MAAe,EAAE;QAC1D,OAAO,IAAI,CAAC,qBAAqB,CAAC,aAAa;YAC7C,QAAQ;YACR,eAAe;gBACb,OAAO;YACT;QACF;IACF;IAEA,+CAA+C;IAC/C,MAAM,UAAU,SAAiB,EAA6D;QAC5F,mDAAmD;QACnD,MAAM,aAAa;QAEnB,IAAI,CAAC,WAAW,IAAI,CAAC,YAAY;YAC/B,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,IAAI;YACF,4CAA4C;YAC5C,yDAAyD;YACzD,mCAAmC;YACnC,OAAO;gBACL,SAAS;gBACT,MAAM;oBACJ,OAAO;oBACP,cAAc;oBACd,SAAS;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;IACF;AACF;AAGO,MAAM,YAAY,IAAI;AAGtB,SAAS,6BAA6B,UAAe;IAC1D,OAAO;QACL,IAAI,WAAW,EAAE;QACjB,OAAO,WAAW,KAAK;QACvB,aAAa,WAAW,WAAW;QACnC,iBAAiB,WAAW,WAAW;QACvC,MAAM,WAAW,IAAI,IAAI,EAAE;QAC3B,UAAU,WAAW,QAAQ;QAC7B,aAAa;YACX,OAAO,CAAC,WAAW,WAAW,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,aAAuB,CAAC;oBACjE,MAAM;wBAAE,OAAO;oBAAW;gBAC5B,CAAC;QACH;QACA,QAAQ;YACN,OAAO,CAAC,WAAW,MAAM,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,QAAe,CAAC;oBACpD,MAAM;wBAAE,KAAK,MAAM,GAAG,IAAI;oBAAM;gBAClC,CAAC;QACH;QACA,OAAO;YACL,OAAO,CAAC,WAAW,MAAM,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,OAAY,QAAkB,CAAC;oBACnE,MAAM;wBACJ,IAAI,CAAC,MAAM,EAAE,OAAO;wBACpB,OAAO;4BAAE,KAAK,MAAM,GAAG,IAAI;wBAAM;oBACnC;gBACF,CAAC;QACH;QACA,YAAY;YACV,iBAAiB;gBACf,QAAQ,WAAW,UAAU,EAAE,iBAAiB,UAAU;gBAC1D,cAAc,WAAW,UAAU,EAAE,iBAAiB,gBAAgB;YACxE;YACA,iBAAiB;gBACf,QAAQ,WAAW,UAAU,EAAE,iBAAiB,UAAU,WAAW,UAAU,EAAE,iBAAiB,UAAU;gBAC5G,cAAc,WAAW,UAAU,EAAE,iBAAiB,gBAAgB,WAAW,UAAU,EAAE,iBAAiB,gBAAgB;YAChI;QACF;QACA,qBAAqB;YACnB,iBAAiB;gBACf,QAAQ;gBACR,cAAc;YAChB;YACA,iBAAiB;gBACf,QAAQ;gBACR,cAAc;YAChB;QACF;QACA,YAAY;YACV,mDAAmD;eAC/C,WAAW,MAAM,GAAG;gBAAC;oBACvB,IAAI;oBACJ,KAAK;oBACL,WAAW;oBACX,OAAO,WAAW,MAAM;oBACxB,MAAM;gBACR;aAAE,GAAG,EAAE;eACH,WAAW,SAAS,GAAG;gBAAC;oBAC1B,IAAI;oBACJ,KAAK;oBACL,WAAW;oBACX,OAAO,WAAW,SAAS;oBAC3B,MAAM;gBACR;aAAE,GAAG,EAAE;eACH,WAAW,gBAAgB,GAAG;gBAAC;oBACjC,IAAI;oBACJ,KAAK;oBACL,WAAW;oBACX,OAAO,WAAW,gBAAgB;oBAClC,MAAM;gBACR;aAAE,GAAG,EAAE;eACH,WAAW,eAAe,GAAG;gBAAC;oBAChC,IAAI;oBACJ,KAAK;oBACL,WAAW;oBACX,OAAO,WAAW,eAAe;oBACjC,MAAM;gBACR;aAAE,GAAG,EAAE;eACH,WAAW,UAAU,GAAG;gBAAC;oBAC3B,IAAI;oBACJ,KAAK;oBACL,WAAW;oBACX,OAAO,WAAW,UAAU;oBAC5B,MAAM;gBACR;aAAE,GAAG,EAAE;eACH,WAAW,UAAU,GAAG;gBAAC;oBAC3B,IAAI;oBACJ,KAAK;oBACL,WAAW;oBACX,OAAO,WAAW,UAAU;oBAC5B,MAAM;gBACR;aAAE,GAAG,EAAE;eACH,WAAW,OAAO,GAAG;gBAAC;oBACxB,IAAI;oBACJ,KAAK;oBACL,WAAW;oBACX,OAAO,WAAW,OAAO;oBACzB,MAAM;gBACR;aAAE,GAAG,EAAE;eACH,WAAW,SAAS,GAAG;gBAAC;oBAC1B,IAAI;oBACJ,KAAK;oBACL,WAAW;oBACX,OAAO,WAAW,SAAS;oBAC3B,MAAM;gBACR;aAAE,GAAG,EAAE;eACH,WAAW,YAAY,GAAG;gBAAC;oBAC7B,IAAI;oBACJ,KAAK;oBACL,WAAW;oBACX,OAAO,WAAW,YAAY;oBAC9B,MAAM;gBACR;aAAE,GAAG,EAAE;eACH,WAAW,SAAS,GAAG;gBAAC;oBAC1B,IAAI;oBACJ,KAAK;oBACL,WAAW;oBACX,OAAO,KAAK,SAAS,CAAC,WAAW,SAAS;oBAC1C,MAAM;gBACR;aAAE,GAAG,EAAE;eACH,WAAW,QAAQ,GAAG;gBAAC;oBACzB,IAAI;oBACJ,KAAK;oBACL,WAAW;oBACX,OAAO,KAAK,SAAS,CAAC,WAAW,QAAQ;oBACzC,MAAM;gBACR;aAAE,GAAG,EAAE;eACH,WAAW,YAAY,GAAG;gBAAC;oBAC7B,IAAI;oBACJ,KAAK;oBACL,WAAW;oBACX,OAAO,KAAK,SAAS,CAAC,WAAW,YAAY;oBAC7C,MAAM;gBACR;aAAE,GAAG,EAAE;eACH,WAAW,kBAAkB,GAAG;gBAAC;oBACnC,IAAI;oBACJ,KAAK;oBACL,WAAW;oBACX,OAAO,KAAK,SAAS,CAAC,WAAW,kBAAkB;oBACnD,MAAM;gBACR;aAAE,GAAG,EAAE;SACR;IACH;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,UAAU,WAAW,CAAC;YAAE,UAAU;YAAM,OAAO;QAAE;QACxE,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;YACrE,OAAO,AAAC,SAAS,IAAI,CAAkB,GAAG,CAAC;QAC7C;QAEA,iDAAiD;QACjD,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,QAAQ,IAAI,CAAC,uBAAuB,SAAS,KAAK;QACpD;QAEA,OAAO,EAAE;IACX,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO,EAAE;IACX;AACF;AAEO,eAAe,eAAe,KAAa;IAChD,IAAI;QACF,MAAM,WAAW,MAAM,UAAU,WAAW,CAAC;YAAE,QAAQ;YAAO,OAAO;QAAG;QACxE,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;YACrE,OAAO,AAAC,SAAS,IAAI,CAAkB,GAAG,CAAC;QAC7C;QACA,OAAO,EAAE;IACX,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,EAAE;IACX;AACF;AAEO,eAAe,wBAAwB,eAAuB;IACnE,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,gBAAgB,CAAC,CAAC;QACpE,MAAM,WAAW,MAAM,UAAU,WAAW,CAAC;YAAE,YAAY;YAAiB,OAAO;QAAI;QACvF,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,gBAAgB,EAAE,CAAC,EAAE;YAC/D,SAAS,SAAS,OAAO;YACzB,YAAY,SAAS,IAAI,GAAI,MAAM,OAAO,CAAC,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,MAAM,GAAG,cAAe;YAClG,OAAO,SAAS,KAAK;QACvB;QAEA,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;YACrE,MAAM,WAAW,AAAC,SAAS,IAAI,CAAkB,GAAG,CAAC;YACrD,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,SAAS,MAAM,CAAC,0BAA0B,EAAE,gBAAgB,CAAC,CAAC;YACzF,OAAO;QACT;QAEA,QAAQ,IAAI,CAAC,CAAC,kCAAkC,EAAE,gBAAgB,CAAC,CAAC;QACpE,OAAO,EAAE;IACX,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,uCAAuC,EAAE,gBAAgB,CAAC,CAAC,EAAE;QAC5E,OAAO,EAAE;IACX;AACF;AAEO,eAAe,eAAe,EAAU;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,UAAU,cAAc,CAAC;QAChD,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;YACrC,OAAO,6BAA6B,SAAS,IAAI;QACnD;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC,EAAE;QAC/C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 708, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Benzochem%20Industries%20v2/edit-13%20%28%20clone%20%29/Benzochem-Industries/user/app/products/%5Bid%5D/page.tsx"], "sourcesContent": ["import type { Metadata } from \"next\"\nimport { notFound } from \"next/navigation\"\nimport { Suspense } from 'react'\nimport ProductDetail from \"@/components/product-detail\"\nimport ProductSpecifications from \"@/components/product-specifications\"\nimport RelatedProducts from \"@/components/related-products\"\nimport { Skeleton } from \"@/components/ui/skeleton\"\nimport { Card } from \"@/components/ui/card\"\nimport { getProductById } from \"@/lib/api-client\"\n\ninterface ProductPageProps {\n  params: {\n    id: string\n  }\n}\n\n// Dynamic rendering - no static generation\n// This ensures all product data is fetched in real-time from the API\nexport const dynamic = 'force-dynamic'\n\nexport async function generateMetadata({ params }: ProductPageProps): Promise<Metadata> {\n  try {\n    const { id } = await params\n    const product = await getProductById(id)\n    \n    if (!product) {\n      return {\n        title: \"Product Not Found | Benzochem Industries\",\n        description: \"The requested product could not be found.\",\n      }\n    }\n    \n    return {\n      title: `${product.title} | Benzochem Industries`,\n      description: product.description,\n    }\n  } catch (error) {\n    return {\n      title: \"Product Not Found | Benzochem Industries\",\n      description: \"The requested product could not be found.\",\n    }\n  }\n}\n\n// Enhanced skeleton component for the page\nfunction ProductPageLoadingSkeleton() {\n  return (\n    <main className=\"flex min-h-screen flex-col pt-20 bg-background\">\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* Skeleton for Enhanced Breadcrumbs */}\n        <div className=\"mb-8\">\n          <Card className=\"vanilla-card border-0 bg-gradient-to-r from-muted/30 to-muted/10 shadow-sm\">\n            <div className=\"p-4\">\n              <div className=\"flex items-center space-x-2\">\n                <Skeleton className=\"h-4 w-4 rounded\" />\n                <Skeleton className=\"h-4 w-16 rounded\" />\n                <Skeleton className=\"h-3 w-3 rounded\" />\n                <Skeleton className=\"h-4 w-4 rounded\" />\n                <Skeleton className=\"h-4 w-20 rounded\" />\n                <Skeleton className=\"h-3 w-3 rounded\" />\n                <Skeleton className=\"h-4 w-4 rounded\" />\n                <Skeleton className=\"h-4 w-24 rounded\" />\n                <Skeleton className=\"h-3 w-3 rounded\" />\n                <Skeleton className=\"h-4 w-4 rounded\" />\n                <Skeleton className=\"h-4 w-32 rounded\" />\n              </div>\n            </div>\n          </Card>\n        </div>\n\n        {/* Skeleton for ProductDetail */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12\">\n          <div className=\"space-y-6\"> {/* Left column: Image Gallery */}\n            <Skeleton className=\"h-[500px] w-full rounded-2xl\" /> {/* Main Image */}\n            <div className=\"grid grid-cols-4 gap-3\"> {/* Thumbnails */}\n              <Skeleton className=\"aspect-square rounded-xl\" />\n              <Skeleton className=\"aspect-square rounded-xl\" />\n              <Skeleton className=\"aspect-square rounded-xl\" />\n              <Skeleton className=\"aspect-square rounded-xl\" />\n            </div>\n            {/* Product Features Skeleton */}\n            <Card className=\"vanilla-card border-0\">\n              <div className=\"p-6\">\n                <Skeleton className=\"h-6 w-40 mb-4 rounded\" />\n                <div className=\"grid grid-cols-2 gap-4\">\n                  {[...Array(4)].map((_, i) => (\n                    <div key={i} className=\"flex items-start space-x-3 p-4 rounded-xl bg-muted/30\">\n                      <Skeleton className=\"h-8 w-8 rounded-xl\" />\n                      <div className=\"flex-1\">\n                        <Skeleton className=\"h-4 w-20 mb-1 rounded\" />\n                        <Skeleton className=\"h-3 w-24 rounded\" />\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </Card>\n          </div>\n          \n          <div className=\"space-y-6\"> {/* Right column: Product Info */}\n            {/* Header Section */}\n            <div className=\"space-y-4\">\n              <div className=\"flex items-start justify-between\">\n                <div className=\"flex items-center space-x-2\">\n                  <Skeleton className=\"h-6 w-24 rounded-full\" />\n                  <Skeleton className=\"h-6 w-20 rounded-full\" />\n                </div>\n                <Skeleton className=\"h-10 w-10 rounded-full\" />\n              </div>\n              \n              <Skeleton className=\"h-10 w-3/4 rounded\" /> {/* Title */}\n              \n              <div className=\"flex items-center space-x-4\">\n                <div className=\"flex space-x-1\">\n                  {[...Array(5)].map((_, i) => (\n                    <Skeleton key={i} className=\"h-4 w-4 rounded\" />\n                  ))}\n                  <Skeleton className=\"h-4 w-24 ml-2 rounded\" />\n                </div>\n                <Skeleton className=\"h-5 w-16 rounded-full\" />\n              </div>\n              \n              {/* Description */}\n              <div className=\"space-y-2\">\n                <Skeleton className=\"h-4 w-full rounded\" />\n                <Skeleton className=\"h-4 w-full rounded\" />\n                <Skeleton className=\"h-4 w-3/4 rounded\" />\n              </div>\n            </div>\n\n            {/* Specifications Tabs Skeleton */}\n            <Card className=\"vanilla-card border-0\">\n              <div className=\"p-6\">\n                <div className=\"grid grid-cols-2 gap-1 bg-muted/50 p-1 rounded-lg mb-6\">\n                  <Skeleton className=\"h-10 rounded-md\" />\n                  <Skeleton className=\"h-10 rounded-md\" />\n                </div>\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\n                  {[...Array(4)].map((_, i) => (\n                    <div key={i} className=\"flex items-center justify-between p-4 rounded-xl bg-muted/30\">\n                      <div className=\"flex items-center space-x-3\">\n                        <Skeleton className=\"h-8 w-8 rounded-xl\" />\n                        <div>\n                          <Skeleton className=\"h-3 w-16 mb-1 rounded\" />\n                          <Skeleton className=\"h-4 w-20 rounded\" />\n                        </div>\n                      </div>\n                      <Skeleton className=\"h-6 w-6 rounded\" />\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </Card>\n\n            {/* Purchase Section Skeleton */}\n            <Card className=\"vanilla-card border-0\">\n              <div className=\"p-6 space-y-6\">\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <Skeleton className=\"h-4 w-16 mb-3 rounded\" />\n                    <Skeleton className=\"h-12 w-full rounded-xl\" />\n                  </div>\n                  <div>\n                    <Skeleton className=\"h-4 w-20 mb-3 rounded\" />\n                    <Skeleton className=\"h-12 w-full rounded-xl\" />\n                  </div>\n                </div>\n                <Skeleton className=\"h-14 w-full rounded-xl\" /> {/* Add to quotation button */}\n                <div className=\"grid grid-cols-2 gap-3\">\n                  <Skeleton className=\"h-12 rounded-xl\" />\n                  <Skeleton className=\"h-12 rounded-xl\" />\n                </div>\n              </div>\n            </Card>\n\n            {/* Trust Indicators Skeleton */}\n            <div className=\"grid grid-cols-3 gap-4\">\n              {[...Array(3)].map((_, i) => (\n                <div key={i} className=\"flex flex-col items-center text-center p-4 rounded-xl bg-muted/30\">\n                  <Skeleton className=\"h-6 w-6 mb-2 rounded\" />\n                  <Skeleton className=\"h-3 w-16 rounded\" />\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Skeleton for ProductSpecifications */}\n        <div className=\"mt-16\">\n          <Skeleton className=\"h-8 w-1/3 mb-6 rounded\" /> {/* Section Title: \"Product Specifications\" */}\n          <div className=\"space-y-4\">\n            {[...Array(3)].map((_, i) => ( // Assuming 3 spec rows for skeleton\n              <div key={i} className=\"grid grid-cols-3 gap-4\"> {/* Key-value pair */}\n                <Skeleton className=\"h-4 w-1/3 rounded\" /> {/* Spec Name */}\n                <Skeleton className=\"h-4 w-2/3 col-span-2 rounded\" /> {/* Spec Value */}\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Skeleton for RelatedProducts */}\n        <div className=\"mt-24 mb-16\">\n          <Skeleton className=\"h-8 w-1/4 mb-8 rounded\" /> {/* Section Title */}\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6\">\n            {[...Array(4)].map((_, i) => (\n              <Card key={i} className=\"vanilla-card border-0\">\n                <div className=\"p-4\">\n                  <Skeleton className=\"aspect-square w-full rounded-lg mb-4\" /> {/* Image */}\n                  <Skeleton className=\"h-6 w-3/4 mb-2 rounded\" /> {/* Title */}\n                  <Skeleton className=\"h-4 w-1/2 rounded\" /> {/* Price */}\n                </div>\n              </Card>\n            ))}\n          </div>\n        </div>\n      </div>\n    </main>\n  );\n}\n\n// Component to fetch and render actual content\nasync function ProductPageContent({ productId }: { productId: string }) {\n  try {\n    const product = await getProductById(productId)\n    \n    if (!product) {\n      notFound()\n    }\n\n    return (\n      <main className=\"flex min-h-screen flex-col pt-20\">\n        <ProductDetail product={product} />\n        \n        <div className=\"container mx-auto px-4 py-8\">\n          <ProductSpecifications product={product} />\n          \n          <RelatedProducts \n            currentProductId={productId}\n            category={product.collections?.edges?.[0]?.node?.title || \"\"}\n          />\n        </div>\n      </main>\n    )\n  } catch (error) {\n    console.error(\"Error in ProductPageContent:\", error)\n    notFound()\n  }\n}\n\nexport default async function ProductPage({ params }: ProductPageProps) {\n  const { id } = await params\n\n  return (\n    <Suspense fallback={<ProductPageLoadingSkeleton />}>\n      <ProductPageContent productId={id} />\n    </Suspense>\n  )\n}"], "names": [], "mappings": ";;;;;;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAUO,MAAM,UAAU;AAEhB,eAAe,iBAAiB,EAAE,MAAM,EAAoB;IACjE,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QACrB,MAAM,UAAU,MAAM,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE;QAErC,IAAI,CAAC,SAAS;YACZ,OAAO;gBACL,OAAO;gBACP,aAAa;YACf;QACF;QAEA,OAAO;YACL,OAAO,GAAG,QAAQ,KAAK,CAAC,uBAAuB,CAAC;YAChD,aAAa,QAAQ,WAAW;QAClC;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YACL,OAAO;YACP,aAAa;QACf;IACF;AACF;AAEA,2CAA2C;AAC3C,SAAS;IACP,qBACE,8OAAC;QAAK,WAAU;kBACd,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,yHAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6HAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC,6HAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC,6HAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC,6HAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC,6HAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC,6HAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC,6HAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC,6HAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC,6HAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC,6HAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC,6HAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO5B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;gCAAY;8CACzB,8OAAC,6HAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiC;8CACrD,8OAAC;oCAAI,WAAU;;wCAAyB;sDACtC,8OAAC,6HAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC,6HAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC,6HAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC,6HAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;8CAGtB,8OAAC,yHAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,6HAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAI,WAAU;0DACZ;uDAAI,MAAM;iDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;wDAAY,WAAU;;0EACrB,8OAAC,6HAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,6HAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,8OAAC,6HAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;;;;;;;;uDAJd;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAapB,8OAAC;4BAAI,WAAU;;gCAAY;8CAEzB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,6HAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC,6HAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;;8DAEtB,8OAAC,6HAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;sDAGtB,8OAAC,6HAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAuB;sDAE3C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;wDACZ;+DAAI,MAAM;yDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,6HAAA,CAAA,WAAQ;gEAAS,WAAU;+DAAb;;;;;sEAEjB,8OAAC,6HAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;;8DAEtB,8OAAC,6HAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;sDAItB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,6HAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC,6HAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC,6HAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;;8CAKxB,8OAAC,yHAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,6HAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC,6HAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;;0DAEtB,8OAAC;gDAAI,WAAU;0DACZ;uDAAI,MAAM;iDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;wDAAY,WAAU;;0EACrB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,6HAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,8OAAC;;0FACC,8OAAC,6HAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;0FACpB,8OAAC,6HAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;;;;;;;;;;;;;0EAGxB,8OAAC,6HAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;uDARZ;;;;;;;;;;;;;;;;;;;;;8CAgBlB,8OAAC,yHAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC,6HAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC,6HAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;;kEAEtB,8OAAC;;0EACC,8OAAC,6HAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC,6HAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;;;;;;;;0DAGxB,8OAAC,6HAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAA2B;0DAC/C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,6HAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC,6HAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAM1B,8OAAC;oCAAI,WAAU;8CACZ;2CAAI,MAAM;qCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;4CAAY,WAAU;;8DACrB,8OAAC,6HAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC,6HAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;2CAFZ;;;;;;;;;;;;;;;;;;;;;;8BAUlB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,6HAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAA2B;sCAC/C,8OAAC;4BAAI,WAAU;sCACZ;mCAAI,MAAM;6BAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;oCAAY,WAAU;;wCAAyB;sDAC9C,8OAAC,6HAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAsB;sDAC1C,8OAAC,6HAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiC;;mCAF7C;;;;;;;;;;;;;;;;8BAShB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,6HAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAA2B;sCAC/C,8OAAC;4BAAI,WAAU;sCACZ;mCAAI,MAAM;6BAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,yHAAA,CAAA,OAAI;oCAAS,WAAU;8CACtB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,6HAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAyC;0DAC7D,8OAAC,6HAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAA2B;0DAC/C,8OAAC,6HAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAsB;;;;;;;mCAJnC;;;;;;;;;;;;;;;;;;;;;;;;;;;AAazB;AAEA,+CAA+C;AAC/C,eAAe,mBAAmB,EAAE,SAAS,EAAyB;IACpE,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE;QAErC,IAAI,CAAC,SAAS;YACZ,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;QACT;QAEA,qBACE,8OAAC;YAAK,WAAU;;8BACd,8OAAC,gIAAA,CAAA,UAAa;oBAAC,SAAS;;;;;;8BAExB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,wIAAA,CAAA,UAAqB;4BAAC,SAAS;;;;;;sCAEhC,8OAAC,kIAAA,CAAA,UAAe;4BACd,kBAAkB;4BAClB,UAAU,QAAQ,WAAW,EAAE,OAAO,CAAC,EAAE,EAAE,MAAM,SAAS;;;;;;;;;;;;;;;;;;IAKpE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;AACF;AAEe,eAAe,YAAY,EAAE,MAAM,EAAoB;IACpE,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;IAErB,qBACE,8OAAC,qMAAA,CAAA,WAAQ;QAAC,wBAAU,8OAAC;;;;;kBACnB,cAAA,8OAAC;YAAmB,WAAW;;;;;;;;;;;AAGrC", "debugId": null}}]}