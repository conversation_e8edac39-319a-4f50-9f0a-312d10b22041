# Lenis Smooth Scroll Implementation

This document outlines the implementation of Lenis smooth scrolling for both the admin and user applications.

## Overview

Lenis is a lightweight, robust, and performant smooth scroll library that provides buttery smooth scrolling experiences. It has been integrated into both the admin and user applications.

## Features Implemented

### 1. Smooth Scrolling
- **Duration**: 1.2 seconds for smooth transitions
- **Easing**: Custom easing function for natural feel
- **Mouse wheel support**: Normalized wheel events
- **Touch support**: Disabled on mobile for better performance
- **Keyboard support**: Works with arrow keys and page up/down

### 2. Components Created

#### LenisProvider
- **Location**: 
  - `admin/src/providers/lenis-provider.tsx`
  - `user/providers/lenis-provider.tsx`
- **Purpose**: Initializes and manages Lenis instance
- **Features**:
  - Global Lenis instance exposure
  - Automatic cleanup on unmount
  - Route change handling
  - HTML class management

#### useLenis Hook
- **Location**: 
  - `admin/src/hooks/use-lenis.ts`
  - `user/hooks/use-lenis.ts`
- **Purpose**: Provides easy access to Lenis functionality
- **Methods**:
  - `scrollTo(target, options)`: Scroll to specific element or position
  - `scrollToTop(options)`: Scroll to top of page
  - `scrollToBottom(options)`: Scroll to bottom of page
  - `stop()`: Stop smooth scrolling
  - `start()`: Resume smooth scrolling

#### ScrollToTop Component
- **Location**: 
  - `admin/src/components/ui/scroll-to-top.tsx`
  - `user/components/ui/scroll-to-top.tsx`
- **Purpose**: Floating button to scroll to top
- **Features**:
  - Appears after scrolling 400px
  - Smooth animation
  - Customizable appearance

#### SmoothScrollLink Component
- **Location**: 
  - `admin/src/components/ui/smooth-scroll-link.tsx`
  - `user/components/ui/smooth-scroll-link.tsx`
- **Purpose**: Link component with smooth scrolling to anchors
- **Features**:
  - Automatic anchor detection
  - Customizable duration and offset
  - Fallback to normal navigation for external links

### 3. CSS Integration

#### Lenis Styles
Added to both `globals.css` files:
```css
/* Lenis smooth scrolling */
html.lenis,
html.lenis body {
  height: auto;
}

.lenis.lenis-smooth {
  scroll-behavior: auto !important;
}

.lenis.lenis-smooth [data-lenis-prevent] {
  overscroll-behavior: contain;
}

.lenis.lenis-stopped {
  overflow: hidden;
}

.lenis.lenis-scrolling iframe {
  pointer-events: none;
}
```

## Usage Examples

### Basic Scrolling
```tsx
import { useLenis } from '@/hooks/use-lenis';

function MyComponent() {
  const { scrollTo, scrollToTop } = useLenis();

  const handleScrollToSection = () => {
    scrollTo('#my-section', { duration: 2 });
  };

  const handleScrollToTop = () => {
    scrollToTop({ duration: 1.5 });
  };

  return (
    <div>
      <button onClick={handleScrollToSection}>Scroll to Section</button>
      <button onClick={handleScrollToTop}>Scroll to Top</button>
    </div>
  );
}
```

### Smooth Scroll Links
```tsx
import { SmoothScrollLink } from '@/components/ui/smooth-scroll-link';

function Navigation() {
  return (
    <nav>
      <SmoothScrollLink href="#about" duration={1.5} offset={-100}>
        About
      </SmoothScrollLink>
      <SmoothScrollLink href="#contact" duration={2}>
        Contact
      </SmoothScrollLink>
    </nav>
  );
}
```

### Preventing Smooth Scroll
To prevent smooth scrolling on specific elements, add the `data-lenis-prevent` attribute:
```html
<div data-lenis-prevent>
  <!-- This content won't be affected by smooth scrolling -->
</div>
```

## Configuration

### Lenis Options
Current configuration in both providers:
```typescript
const lenis = new Lenis({
  duration: 1.2,                    // Animation duration
  easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)), // Custom easing
  direction: "vertical",            // Scroll direction
  gestureDirection: "vertical",     // Gesture direction
  smooth: true,                     // Enable smooth scrolling
  mouseMultiplier: 1,              // Mouse wheel sensitivity
  smoothTouch: false,              // Disable on touch devices
  touchMultiplier: 2,              // Touch sensitivity
  infinite: false,                 // Disable infinite scroll
  normalizeWheel: true,            // Normalize wheel events
  wheelMultiplier: 1,              // Wheel sensitivity
});
```

## Integration Points

### Admin Application
- Integrated in `admin/src/app/layout.tsx`
- Wraps all content with `LenisProvider`
- Includes `ScrollToTop` component

### User Application
- Integrated in `user/app/layout.tsx`
- Wraps all content with `LenisProvider`
- Includes `ScrollToTop` component

## Performance Considerations

1. **RAF Loop**: Uses requestAnimationFrame for smooth 60fps animations
2. **Memory Management**: Proper cleanup prevents memory leaks
3. **Touch Optimization**: Disabled on touch devices for better performance
4. **Route Handling**: Temporarily stops during navigation to prevent conflicts

## Browser Support

Lenis supports all modern browsers:
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Troubleshooting

### Common Issues

1. **Scrolling not working**: Check if `LenisProvider` is properly wrapped around your app
2. **Performance issues**: Ensure RAF loop is properly cleaned up
3. **Touch scrolling problems**: `smoothTouch` is disabled by default for better mobile performance
4. **Route navigation conflicts**: The provider handles route changes automatically

### Debug Mode
To debug Lenis, you can access the global instance:
```javascript
// In browser console
window.lenis.stop(); // Stop scrolling
window.lenis.start(); // Resume scrolling
```

## Future Enhancements

Potential improvements:
1. **Scroll progress indicator**: Visual indicator of scroll position
2. **Parallax integration**: Enhanced parallax effects with Lenis
3. **Scroll-triggered animations**: GSAP integration for scroll animations
4. **Custom easing presets**: Multiple easing options
5. **Scroll snap points**: Defined scroll positions

## Dependencies

- `lenis`: ^1.1.13 (latest version)
- `react`: ^19.0.0
- `next`: 15.3.4+

## Files Modified/Created

### Admin Application
- `src/providers/lenis-provider.tsx` (new)
- `src/hooks/use-lenis.ts` (new)
- `src/components/ui/scroll-to-top.tsx` (new)
- `src/components/ui/smooth-scroll-link.tsx` (new)
- `src/app/layout.tsx` (modified)
- `src/app/globals.css` (modified)

### User Application
- `providers/lenis-provider.tsx` (new)
- `hooks/use-lenis.ts` (new)
- `components/ui/scroll-to-top.tsx` (new)
- `components/ui/smooth-scroll-link.tsx` (new)
- `app/layout.tsx` (modified)
- `app/globals.css` (modified)

The implementation is complete and ready for use in both applications.