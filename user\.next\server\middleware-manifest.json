{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_a1b8b59d._.js", "server/edge/chunks/[root of the server]__10ae24bd._.js", "server/edge/chunks/edge-wrapper_321a5c3e.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!.+.[w]+$|_next).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!.+.[w]+$|_next).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "howdG+nrZ4Du0yeMBuqDyNpHJTR1UPzPpWT90KVOfdU=", "__NEXT_PREVIEW_MODE_ID": "eee8329320b1d4fc6c0ccffe47be7173", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "17fe94c75da4fd0167fb18c9304fc59726c58cdb3f7ca5ece6c29cab141b8839", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "17ed866e32e4e29322003d5c300a95c26d03b17d587c9d563655b22d0f0c17d0"}}}, "sortedMiddleware": ["/"], "functions": {}}