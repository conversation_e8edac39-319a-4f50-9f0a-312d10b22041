{"name": "my-v0-project", "version": "0.1.0", "private": true, "main": "app.js", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "node app.js", "lint": "next lint"}, "dependencies": {"@emotion/is-prop-valid": "latest", "@eslint/eslintrc": "latest", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "latest", "@radix-ui/react-alert-dialog": "latest", "@radix-ui/react-aspect-ratio": "latest", "@radix-ui/react-avatar": "latest", "@radix-ui/react-checkbox": "latest", "@radix-ui/react-collapsible": "latest", "@radix-ui/react-context-menu": "latest", "@radix-ui/react-dialog": "latest", "@radix-ui/react-dropdown-menu": "latest", "@radix-ui/react-hover-card": "latest", "@radix-ui/react-icons": "latest", "@radix-ui/react-label": "latest", "@radix-ui/react-menubar": "latest", "@radix-ui/react-navigation-menu": "latest", "@radix-ui/react-popover": "latest", "@radix-ui/react-progress": "latest", "@radix-ui/react-radio-group": "latest", "@radix-ui/react-scroll-area": "latest", "@radix-ui/react-select": "latest", "@radix-ui/react-separator": "latest", "@radix-ui/react-slider": "latest", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "latest", "@radix-ui/react-tabs": "latest", "@radix-ui/react-toast": "latest", "@radix-ui/react-toggle": "latest", "@radix-ui/react-toggle-group": "latest", "@radix-ui/react-tooltip": "latest", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "crypto": "latest", "date-fns": "4.1.0", "embla-carousel-react": "latest", "express": "^4.18.2", "framer-motion": "latest", "input-otp": "latest", "lenis": "^1.3.4", "lucide-react": "^0.454.0", "next": "15.2.4", "next-themes": "latest", "path": "latest", "react": "^19", "react-country-flag": "^3.1.0", "react-day-picker": "latest", "react-dom": "^19", "react-hook-form": "latest", "react-resizable-panels": "latest", "recharts": "latest", "sonner": "latest", "svix": "^1.66.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "url": "latest", "vaul": "latest", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5"}}