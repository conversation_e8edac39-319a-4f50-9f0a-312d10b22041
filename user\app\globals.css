@tailwind base;
@tailwind components;
@tailwind utilities;

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.75rem;
  /* Vanilla Latte Light Theme - Based on #F3E5C3 */
  --background: oklch(0.95 0.02 75); /* Warm vanilla cream */
  --foreground: oklch(0.25 0.05 160); /* Deep teal-green for text */
  --card: oklch(0.97 0.015 70); /* Slightly lighter cream for cards */
  --card-foreground: oklch(0.25 0.05 160);
  --popover: oklch(0.97 0.015 70);
  --popover-foreground: oklch(0.25 0.05 160);
  --primary: oklch(0.35 0.08 165); /* Rich teal-green */
  --primary-foreground: oklch(0.97 0.015 70);
  --secondary: oklch(0.88 0.025 75); /* Warm muted vanilla */
  --secondary-foreground: oklch(0.35 0.08 165);
  --muted: oklch(0.88 0.025 75);
  --muted-foreground: oklch(0.5 0.04 160);
  --accent: oklch(0.85 0.03 80); /* Soft warm accent */                                                                                   
  --accent-foreground: oklch(0.35 0.08 165);
  --destructive: oklch(0.55 0.2 25); /* Warm red */
  --border: oklch(0.82 0.03 75); /* Soft vanilla border */
  --input: oklch(0.9 0.02 75); /* Light vanilla input */
  --ring: oklch(0.45 0.06 165); /* Teal focus ring */
  --chart-1: oklch(0.65 0.15 45); /* Warm amber */
  --chart-2: oklch(0.6 0.12 180); /* Soft teal */
  --chart-3: oklch(0.55 0.1 200); /* Muted blue */
  --chart-4: oklch(0.7 0.18 85); /* Warm green */
  --chart-5: oklch(0.75 0.16 60); /* Golden yellow */
  --sidebar: oklch(0.94 0.02 75);
  --sidebar-foreground: oklch(0.25 0.05 160);
  --sidebar-primary: oklch(0.35 0.08 165);
  --sidebar-primary-foreground: oklch(0.97 0.015 70);
  --sidebar-accent: oklch(0.88 0.025 75);
  --sidebar-accent-foreground: oklch(0.35 0.08 165);
  --sidebar-border: oklch(0.82 0.03 75);
  --sidebar-ring: oklch(0.45 0.06 165);
}

.dark {
  /* Elegant Dark Theme - Warm and sophisticated */
  --background: oklch(0.1 0.02 220); /* Deep warm charcoal */
  --foreground: oklch(0.95 0.015 70); /* Warm cream text */
  --card: oklch(0.15 0.02 215); /* Slightly lighter charcoal for cards */
  --card-foreground: oklch(0.95 0.015 70);
  --popover: oklch(0.15 0.02 215);
  --popover-foreground: oklch(0.95 0.015 70);
  --primary: oklch(0.7 0.08 165); /* Bright teal accent */
  --primary-foreground: oklch(0.1 0.02 220);
  --secondary: oklch(0.2 0.02 210); /* Warm dark secondary */
  --secondary-foreground: oklch(0.95 0.015 70);
  --muted: oklch(0.2 0.02 210);
  --muted-foreground: oklch(0.7 0.03 160);
  --accent: oklch(0.25 0.03 200); /* Subtle warm accent */
  --accent-foreground: oklch(0.95 0.015 70);
  --destructive: oklch(0.65 0.18 25); /* Warm red for dark */
  --border: oklch(0.25 0.02 210); /* Subtle warm border */
  --input: oklch(0.2 0.02 210); /* Dark warm input */
  --ring: oklch(0.6 0.08 165); /* Bright teal focus ring */
  --chart-1: oklch(0.7 0.18 45); /* Bright amber */
  --chart-2: oklch(0.65 0.15 180); /* Bright teal */
  --chart-3: oklch(0.6 0.12 200); /* Soft blue */
  --chart-4: oklch(0.75 0.2 85); /* Bright green */
  --chart-5: oklch(0.8 0.18 60); /* Golden yellow */
  --sidebar: oklch(0.12 0.02 220);
  --sidebar-foreground: oklch(0.95 0.015 70);
  --sidebar-primary: oklch(0.7 0.08 165);
  --sidebar-primary-foreground: oklch(0.1 0.02 220);
  --sidebar-accent: oklch(0.2 0.02 210);
  --sidebar-accent-foreground: oklch(0.95 0.015 70);
  --sidebar-border: oklch(0.25 0.02 210);
  --sidebar-ring: oklch(0.6 0.08 165);
}

@layer base {
  * {
    border-color: var(--border);
    outline-color: rgba(var(--ring), 0.5);
  }
  body {
    background-color: var(--background);
    color: var(--foreground);
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  /* Lenis smooth scrolling */
  html.lenis,
  html.lenis body {
    height: auto;
  }

  .lenis.lenis-smooth {
    scroll-behavior: auto !important;
  }

  .lenis.lenis-smooth [data-lenis-prevent] {
    overscroll-behavior: contain;
  }

  .lenis.lenis-stopped {
    overflow: hidden;
  }

  .lenis.lenis-scrolling iframe {
    pointer-events: none;
  }
}

/* Modern Scrollbar Styles - Matching Vanilla Latte Theme */
@layer base {
  /* Webkit Scrollbar Styles */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: oklch(0.92 0.02 75);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: oklch(0.75 0.04 165);
    border-radius: 4px;
    transition: background-color 0.2s ease;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: oklch(0.65 0.06 165);
  }

  ::-webkit-scrollbar-thumb:active {
    background: oklch(0.55 0.08 165);
  }

  ::-webkit-scrollbar-corner {
    background: oklch(0.92 0.02 75);
  }

  /* Dark theme scrollbar */
  .dark ::-webkit-scrollbar-track {
    background: oklch(0.18 0.02 215);
  }

  .dark ::-webkit-scrollbar-thumb {
    background: oklch(0.4 0.04 200);
  }

  .dark ::-webkit-scrollbar-thumb:hover {
    background: oklch(0.5 0.06 180);
  }

  .dark ::-webkit-scrollbar-thumb:active {
    background: oklch(0.6 0.08 165);
  }

  .dark ::-webkit-scrollbar-corner {
    background: oklch(0.18 0.02 215);
  }

  /* Firefox Scrollbar Styles */
  * {
    scrollbar-width: thin;
    scrollbar-color: oklch(0.75 0.04 165) oklch(0.92 0.02 75);
  }

  .dark * {
    scrollbar-color: oklch(0.4 0.04 200) oklch(0.18 0.02 215);
  }

  /* Custom scrollbar for specific containers */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: oklch(0.75 0.04 165) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: oklch(0.75 0.04 165 / 0.6);
    border-radius: 3px;
    transition: all 0.2s ease;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: oklch(0.65 0.06 165 / 0.8);
  }

  .dark .custom-scrollbar {
    scrollbar-color: oklch(0.4 0.04 200) transparent;
  }

  .dark .custom-scrollbar::-webkit-scrollbar-thumb {
    background: oklch(0.4 0.04 200 / 0.6);
  }

  .dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: oklch(0.5 0.06 180 / 0.8);
  }

  /* Thin scrollbar variant */
  .thin-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: oklch(0.75 0.04 165 / 0.4) transparent;
  }

  .thin-scrollbar::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  .thin-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .thin-scrollbar::-webkit-scrollbar-thumb {
    background: oklch(0.75 0.04 165 / 0.4);
    border-radius: 2px;
    transition: all 0.2s ease;
  }

  .thin-scrollbar::-webkit-scrollbar-thumb:hover {
    background: oklch(0.65 0.06 165 / 0.7);
  }

  .dark .thin-scrollbar {
    scrollbar-color: oklch(0.4 0.04 200 / 0.4) transparent;
  }

  .dark .thin-scrollbar::-webkit-scrollbar-thumb {
    background: oklch(0.4 0.04 200 / 0.4);
  }

  .dark .thin-scrollbar::-webkit-scrollbar-thumb:hover {
    background: oklch(0.5 0.06 180 / 0.7);
  }
}

/* Custom enhancements for the vanilla latte theme */
@layer components {
  .vanilla-card {
    @apply bg-card border border-border rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200;
  }
  
  .vanilla-gradient {
    background: linear-gradient(135deg, oklch(0.97 0.015 70) 0%, oklch(0.94 0.02 75) 100%);
  }
  
  .dark .vanilla-gradient {
    background: linear-gradient(135deg, oklch(0.15 0.02 215) 0%, oklch(0.12 0.02 220) 100%);
  }
  
  .warm-shadow {
    box-shadow: 0 4px 6px -1px oklch(0.8 0.03 75 / 0.1), 0 2px 4px -1px oklch(0.8 0.03 75 / 0.06);
  }
  
  .dark .warm-shadow {
    box-shadow: 0 4px 6px -1px oklch(0.05 0.02 220 / 0.3), 0 2px 4px -1px oklch(0.05 0.02 220 / 0.2);
  }

  /* Premium waiting list enhancements */
  .premium-glow {
    box-shadow: 
      0 0 20px oklch(0.35 0.08 165 / 0.1),
      0 4px 6px -1px oklch(0.8 0.03 75 / 0.1), 
      0 2px 4px -1px oklch(0.8 0.03 75 / 0.06);
  }
  
  .dark .premium-glow {
    box-shadow: 
      0 0 20px oklch(0.7 0.08 165 / 0.2),
      0 4px 6px -1px oklch(0.05 0.02 220 / 0.3), 
      0 2px 4px -1px oklch(0.05 0.02 220 / 0.2);
  }

  .status-pending {
    background: linear-gradient(135deg, oklch(0.65 0.15 45 / 0.05) 0%, oklch(0.65 0.15 45 / 0.1) 100%);
    border-color: oklch(0.65 0.15 45 / 0.2);
  }

  .status-approved {
    background: linear-gradient(135deg, oklch(0.7 0.18 85 / 0.05) 0%, oklch(0.7 0.18 85 / 0.1) 100%);
    border-color: oklch(0.7 0.18 85 / 0.2);
  }

  .status-rejected {
    background: linear-gradient(135deg, oklch(0.55 0.2 25 / 0.05) 0%, oklch(0.55 0.2 25 / 0.1) 100%);
    border-color: oklch(0.55 0.2 25 / 0.2);
  }

  .dark .status-pending {
    background: linear-gradient(135deg, oklch(0.7 0.18 45 / 0.05) 0%, oklch(0.7 0.18 45 / 0.1) 100%);
    border-color: oklch(0.7 0.18 45 / 0.2);
  }

  .dark .status-approved {
    background: linear-gradient(135deg, oklch(0.75 0.2 85 / 0.05) 0%, oklch(0.75 0.2 85 / 0.1) 100%);
    border-color: oklch(0.75 0.2 85 / 0.2);
  }

  .dark .status-rejected {
    background: linear-gradient(135deg, oklch(0.65 0.18 25 / 0.05) 0%, oklch(0.65 0.18 25 / 0.1) 100%);
    border-color: oklch(0.65 0.18 25 / 0.2);
  }

  /* Animated background elements */
  .floating-element {
    animation: float 6s ease-in-out infinite;
  }

  .floating-element:nth-child(2) {
    animation-delay: -2s;
  }

  .floating-element:nth-child(3) {
    animation-delay: -4s;
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px) scale(1);
      opacity: 0.3;
    }
    50% {
      transform: translateY(-20px) scale(1.05);
      opacity: 0.6;
    }
  }

  /* Premium button enhancements */
  .premium-button {
    background: linear-gradient(135deg, oklch(0.35 0.08 165) 0%, oklch(0.4 0.1 170) 100%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
  }

  .premium-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, oklch(0.9 0.02 165 / 0.2), transparent);
    transition: left 0.5s;
  }

  .premium-button:hover::before {
    left: 100%;
  }

  .premium-button:hover {
    background: linear-gradient(135deg, oklch(0.4 0.1 165) 0%, oklch(0.45 0.12 170) 100%);
    transform: translateY(-1px);
    box-shadow: 0 8px 25px oklch(0.35 0.08 165 / 0.3);
  }

  .dark .premium-button {
    background: linear-gradient(135deg, oklch(0.7 0.08 165) 0%, oklch(0.75 0.1 170) 100%);
  }

  .dark .premium-button:hover {
    background: linear-gradient(135deg, oklch(0.75 0.1 165) 0%, oklch(0.8 0.12 170) 100%);
    box-shadow: 0 8px 25px oklch(0.7 0.08 165 / 0.4);
  }
}