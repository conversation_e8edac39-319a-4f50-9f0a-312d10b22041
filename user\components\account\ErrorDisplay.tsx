"use client"

import { motion } from "framer-motion"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { LogOut, AlertCircle } from "lucide-react"

interface ErrorDisplayProps {
  error: string | null;
  redirectToLogin: () => void;
}

export default function ErrorDisplay({ error, redirectToLogin }: ErrorDisplayProps) {
  if (!error) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card>
        <CardHeader>
          <CardTitle>Session Expired</CardTitle>
          <CardDescription>
            Your session has expired or you're not properly logged in
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert className="bg-yellow-50 text-yellow-800 border-yellow-200">
            <AlertCircle className="h-4 w-4 mr-2" />
            <AlertDescription>
              {error}
            </AlertDescription>
          </Alert>
          
          <div className="text-center py-6">
            <p className="mb-4">Please sign in again to access your account dashboard</p>
            <Button 
              onClick={redirectToLogin}
              className="bg-teal-600 hover:bg-teal-700"
            >
              <LogOut className="h-4 w-4 mr-2" />
              Go to Login Page
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}