// Server-side session management using secure HTTP cookies
// Replaces localStorage usage for sensitive session data

export interface SessionData {
  userId: string
  email: string
  timestamp: string
  version: string
}

const SESSION_COOKIE_NAME = 'user_session'
const SESSION_VERSION = '1.0'
const SESSION_MAX_AGE = 30 * 24 * 60 * 60 // 30 days in seconds

export class SessionStorage {
  // Check if session exists
  static hasSession(): boolean {
    if (typeof window === 'undefined') return false
    
    try {
      const cookieValue = this.getCookieValue(SESSION_COOKIE_NAME)
      if (!cookieValue) return false
      
      const data: SessionData = JSON.parse(decodeURIComponent(cookieValue))
      return data && data.userId && data.email && data.timestamp && data.version === SESSION_VERSION
    } catch (error) {
      console.error('Error checking session:', error)
      return false
    }
  }

  // Get stored session data
  static getSession(): SessionData | null {
    if (typeof window === 'undefined') return null
    
    try {
      const cookieValue = this.getCookieValue(SESSION_COOKIE_NAME)
      if (!cookieValue) return null
      
      const data: SessionData = JSON.parse(decodeURIComponent(cookieValue))
      
      // Validate data structure
      if (!data.userId || !data.email || !data.timestamp || data.version !== SESSION_VERSION) {
        this.clearSession()
        return null
      }
      
      // Check if session is expired (30 days)
      const sessionDate = new Date(data.timestamp)
      const now = new Date()
      const diffDays = Math.ceil((now.getTime() - sessionDate.getTime()) / (1000 * 60 * 60 * 24))
      
      if (diffDays > 30) {
        this.clearSession()
        return null
      }
      
      return data
    } catch (error) {
      console.error('Error getting session:', error)
      this.clearSession()
      return null
    }
  }

  // Store session data in secure cookie
  static setSession(userId: string, email: string): boolean {
    if (typeof window === 'undefined') return false
    
    try {
      const sessionData: SessionData = {
        userId,
        email,
        timestamp: new Date().toISOString(),
        version: SESSION_VERSION
      }
      
      const cookieValue = encodeURIComponent(JSON.stringify(sessionData))
      const expires = new Date(Date.now() + SESSION_MAX_AGE * 1000).toUTCString()
      
      // Set secure cookie with proper attributes
      document.cookie = `${SESSION_COOKIE_NAME}=${cookieValue}; expires=${expires}; path=/; SameSite=Strict; Secure=${location.protocol === 'https:'}`
      
      console.log('🔐 User session stored securely', {
        userId,
        email,
        timestamp: sessionData.timestamp
      })
      
      return true
    } catch (error) {
      console.error('Error storing session:', error)
      return false
    }
  }

  // Clear session cookie
  static clearSession(): void {
    if (typeof window === 'undefined') return
    
    try {
      // Set cookie with past expiration date to delete it
      document.cookie = `${SESSION_COOKIE_NAME}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Strict`
      console.log('🔐 User session cleared')
    } catch (error) {
      console.error('Error clearing session:', error)
    }
  }

  // Get session age in days
  static getSessionAge(): number {
    const session = this.getSession()
    if (!session) return 0
    
    const sessionDate = new Date(session.timestamp)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - sessionDate.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    return diffDays
  }

  // Check if session needs renewal (after 25 days, prompt for renewal)
  static needsRenewal(): boolean {
    return this.getSessionAge() > 25
  }

  // Utility method to get cookie value by name
  private static getCookieValue(name: string): string | null {
    if (typeof window === 'undefined') return null
    
    const nameEQ = name + "="
    const ca = document.cookie.split(';')
    
    for (let i = 0; i < ca.length; i++) {
      let c = ca[i]
      while (c.charAt(0) === ' ') c = c.substring(1, c.length)
      if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length)
    }
    
    return null
  }

  // Update session timestamp (extend session)
  static refreshSession(): boolean {
    const session = this.getSession()
    if (!session) return false
    
    return this.setSession(session.userId, session.email)
  }
}

// Session utility functions for compatibility
export const sessionUtils = {
  // Get current user session
  getCurrentUserSession(): { userId: string; email: string } | null {
    const session = SessionStorage.getSession()
    if (!session) return null
    
    return {
      userId: session.userId,
      email: session.email
    }
  },

  // Set current user session
  setCurrentUserSession(session: { userId: string; email: string } | null): void {
    if (session) {
      SessionStorage.setSession(session.userId, session.email)
    } else {
      SessionStorage.clearSession()
    }
  },

  // Check if session exists
  hasSession(): boolean {
    return SessionStorage.hasSession()
  },

  // Clear session
  clearSession(): void {
    SessionStorage.clearSession()
  },

  // Refresh session
  refreshSession(): boolean {
    return SessionStorage.refreshSession()
  }
}