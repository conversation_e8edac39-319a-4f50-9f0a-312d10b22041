import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'

const ANONYMOUS_ID_COOKIE_NAME = 'anonymous_id'
const ADMIN_API_URL = process.env.NEXT_PUBLIC_ADMIN_API_URL || 'http://localhost:3001'

// POST - Link anonymous consent to user account when they register/login
export async function POST(request: NextRequest) {
  try {
    const { userId, userEmail, firstName, lastName } = await request.json()

    // Validate required fields
    if (!userId || !userEmail || !firstName || !lastName) {
      return NextResponse.json(
        { error: 'Missing required user information' },
        { status: 400 }
      )
    }

    // Get anonymous ID from cookie
    const cookieStore = await cookies()
    const anonymousIdCookie = cookieStore.get(ANONYMOUS_ID_COOKIE_NAME)

    if (!anonymousIdCookie || !anonymousIdCookie.value) {
      return NextResponse.json({
        success: false,
        message: 'No anonymous consent to link'
      })
    }

    const anonymousId = anonymousIdCookie.value

    // Call admin API to link consent
    try {
      const response = await fetch(`${ADMIN_API_URL}/api/public/cookie-consent`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          anonymousId,
          userId,
          userEmail,
          firstName,
          lastName
        })
      })

      if (response.ok) {
        const result = await response.json()
        
        // Clear anonymous ID cookie since it's now linked
        const responseObj = NextResponse.json({
          success: true,
          action: result.action,
          message: 'Anonymous consent linked successfully'
        })

        responseObj.cookies.set({
          name: ANONYMOUS_ID_COOKIE_NAME,
          value: '',
          maxAge: 0,
          path: '/',
          sameSite: 'strict',
          secure: process.env.NODE_ENV === 'production'
        })

        console.log(`🍪 Anonymous consent linked for user: ${userEmail}`)
        return responseObj
      } else {
        console.error('Failed to link consent:', response.status)
        return NextResponse.json({
          success: false,
          message: 'Failed to link consent'
        })
      }
    } catch (linkError) {
      console.error('Consent linking failed:', linkError)
      return NextResponse.json({
        success: false,
        message: 'Consent linking failed'
      })
    }

  } catch (error) {
    console.error('Link consent API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}