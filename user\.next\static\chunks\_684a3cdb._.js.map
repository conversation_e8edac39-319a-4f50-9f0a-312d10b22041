{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Benzochem%20Industries%20v2/edit-13%20%28%20clone%20%29/Benzochem-Industries/user/components/add-to-quotation-button.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { useRouter } from \"next/navigation\"\nimport { useAuth } from \"@/contexts/auth-context\"\nimport { useSecureQuotation } from \"@/hooks/use-secure-quotation\"\nimport { Button } from \"@/components/ui/button\"\nimport { FileText, Plus } from \"lucide-react\"\nimport { toast } from \"sonner\"\n\ninterface AddToQuotationButtonProps {\n  productId: string\n  variantId: string\n  name: string\n  category: string\n  image: string\n  price: number\n  quantity?: number\n  unit?: string\n  packageSize?: string\n  disabled?: boolean\n  className?: string\n}\n\nexport default function AddToQuotationButton({\n  productId,\n  variantId,\n  name,\n  category,\n  image,\n  price,\n  quantity = 1,\n  unit = \"KG\",\n  packageSize,\n  disabled = false,\n  className\n}: AddToQuotationButtonProps) {\n  const { user } = useAuth()\n  const { addToQuotation } = useSecureQuotation()\n  const router = useRouter()\n  const [isLoading, setIsLoading] = useState(false)\n\n  const handleAddToQuotation = async () => {\n    if (!user) {\n      toast.error(\"Please log in to request quotations\", {\n        action: {\n          label: \"Login\",\n          onClick: () => router.push(\"/login\")\n        }\n      })\n      return\n    }\n\n    try {\n      setIsLoading(true)\n      \n      await addToQuotation({\n        productId,\n        variantId,\n        name,\n        category,\n        image,\n        price,\n        quantity,\n        unit: packageSize || unit,\n        specifications: packageSize ? `Package Size: ${packageSize}` : undefined\n      })\n    } catch (error) {\n      console.error(\"Error adding to quotation:\", error)\n      toast.error(\"Failed to add item to quotation.\")\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  return (\n    <Button\n      onClick={handleAddToQuotation}\n      disabled={disabled || isLoading}\n      className={`border shadow-xs bg-accent text-accent-foreground dark:bg-input/60 dark:border-input dark:hover:bg-input/50 disabled:opacity-50 disabled:cursor-not-allowed ${className}`}\n    >\n      <FileText className=\"h-4 w-4 mr-2\" />\n      {isLoading ? \"Adding...\" : \"Request Quote\"}\n    </Button>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAwBe,SAAS,qBAAqB,EAC3C,SAAS,EACT,SAAS,EACT,IAAI,EACJ,QAAQ,EACR,KAAK,EACL,KAAK,EACL,WAAW,CAAC,EACZ,OAAO,IAAI,EACX,WAAW,EACX,WAAW,KAAK,EAChB,SAAS,EACiB;;IAC1B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,qBAAkB,AAAD;IAC5C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,uBAAuB;QAC3B,IAAI,CAAC,MAAM;YACT,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,uCAAuC;gBACjD,QAAQ;oBACN,OAAO;oBACP,SAAS,IAAM,OAAO,IAAI,CAAC;gBAC7B;YACF;YACA;QACF;QAEA,IAAI;YACF,aAAa;YAEb,MAAM,eAAe;gBACnB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA,MAAM,eAAe;gBACrB,gBAAgB,cAAc,CAAC,cAAc,EAAE,aAAa,GAAG;YACjE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC,8HAAA,CAAA,SAAM;QACL,SAAS;QACT,UAAU,YAAY;QACtB,WAAW,CAAC,4JAA4J,EAAE,WAAW;;0BAErL,6LAAC,iNAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YACnB,YAAY,cAAc;;;;;;;AAGjC;GA7DwB;;QAaL,+HAAA,CAAA,UAAO;QACG,uIAAA,CAAA,qBAAkB;QAC9B,qIAAA,CAAA,YAAS;;;KAfF", "debugId": null}}, {"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Benzochem%20Industries%20v2/edit-13%20%28%20clone%20%29/Benzochem-Industries/user/lib/image-utils.ts"], "sourcesContent": ["// Utility functions for handling images\n\n/**\n * Normalizes image URLs by removing remove.bg markers\n * @param url - The image URL to normalize\n * @returns The normalized URL\n */\nexport function normalizeImageUrl(url: string): string {\n  if (!url) return url\n  \n  // Handle remove.bg processed images by removing the marker\n  if (url.startsWith('data:image/removebg-')) {\n    return url.replace('data:image/removebg-', 'data:image/')\n  }\n  \n  return url\n}\n\n/**\n * Gets the first image URL from a product, handling both admin API and legacy formats\n * @param product - The product object\n * @returns The first image URL or a placeholder\n */\nexport function getProductImageUrl(product: any): string {\n  // Handle new admin API format\n  if (product.images && Array.isArray(product.images) && product.images.length > 0) {\n    const firstImage = product.images[0]\n    const url = typeof firstImage === 'string' ? firstImage : firstImage.url\n    return normalizeImageUrl(url)\n  }\n  \n  // Handle legacy Shopify format\n  const legacyUrl = product.images?.edges?.[0]?.node?.url ||\n                   product.media?.edges?.[0]?.node?.image?.url ||\n                   product.featuredMedia?.preview?.image?.url\n  \n  return legacyUrl ? normalizeImageUrl(legacyUrl) : '/placeholder-chemical.jpg'\n}\n\n/**\n * Gets all image URLs from a product, handling both admin API and legacy formats\n * @param product - The product object\n * @returns Array of image URLs\n */\nexport function getProductImageUrls(product: any): string[] {\n  let images: string[] = []\n  \n  // Handle new admin API format\n  if (product.images && Array.isArray(product.images)) {\n    images = product.images.map((img: any) => {\n      const url = typeof img === 'string' ? img : img.url\n      return normalizeImageUrl(url)\n    }).filter(Boolean)\n  }\n  \n  // Handle legacy Shopify format fallback\n  if (images.length === 0) {\n    const legacyImages = [\n      product.featuredMedia?.preview?.image?.url,\n      ...(product.media?.edges ? product.media.edges.map((edge: any) => edge.node.image.url) : [])\n    ].filter(Boolean).map(normalizeImageUrl)\n    images = legacyImages\n  }\n\n  // If no images are available, use a placeholder\n  if (images.length === 0) {\n    images.push('/placeholder-chemical.jpg')\n  }\n\n  return images\n}\n\n/**\n * Gets the category from a product, handling both admin API and legacy formats\n * @param product - The product object\n * @returns The category name\n */\nexport function getProductCategory(product: any): string {\n  // Handle new admin API format\n  if (product.collections && Array.isArray(product.collections) && product.collections.length > 0) {\n    return product.collections[0]\n  }\n  \n  // Handle legacy Shopify format\n  return product.collections?.edges?.[0]?.node?.title || 'Chemical Products'\n}"], "names": [], "mappings": "AAAA,wCAAwC;AAExC;;;;CAIC;;;;;;AACM,SAAS,kBAAkB,GAAW;IAC3C,IAAI,CAAC,KAAK,OAAO;IAEjB,2DAA2D;IAC3D,IAAI,IAAI,UAAU,CAAC,yBAAyB;QAC1C,OAAO,IAAI,OAAO,CAAC,wBAAwB;IAC7C;IAEA,OAAO;AACT;AAOO,SAAS,mBAAmB,OAAY;IAC7C,8BAA8B;IAC9B,IAAI,QAAQ,MAAM,IAAI,MAAM,OAAO,CAAC,QAAQ,MAAM,KAAK,QAAQ,MAAM,CAAC,MAAM,GAAG,GAAG;QAChF,MAAM,aAAa,QAAQ,MAAM,CAAC,EAAE;QACpC,MAAM,MAAM,OAAO,eAAe,WAAW,aAAa,WAAW,GAAG;QACxE,OAAO,kBAAkB;IAC3B;IAEA,+BAA+B;IAC/B,MAAM,YAAY,QAAQ,MAAM,EAAE,OAAO,CAAC,EAAE,EAAE,MAAM,OACnC,QAAQ,KAAK,EAAE,OAAO,CAAC,EAAE,EAAE,MAAM,OAAO,OACxC,QAAQ,aAAa,EAAE,SAAS,OAAO;IAExD,OAAO,YAAY,kBAAkB,aAAa;AACpD;AAOO,SAAS,oBAAoB,OAAY;IAC9C,IAAI,SAAmB,EAAE;IAEzB,8BAA8B;IAC9B,IAAI,QAAQ,MAAM,IAAI,MAAM,OAAO,CAAC,QAAQ,MAAM,GAAG;QACnD,SAAS,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAC;YAC3B,MAAM,MAAM,OAAO,QAAQ,WAAW,MAAM,IAAI,GAAG;YACnD,OAAO,kBAAkB;QAC3B,GAAG,MAAM,CAAC;IACZ;IAEA,wCAAwC;IACxC,IAAI,OAAO,MAAM,KAAK,GAAG;QACvB,MAAM,eAAe;YACnB,QAAQ,aAAa,EAAE,SAAS,OAAO;eACnC,QAAQ,KAAK,EAAE,QAAQ,QAAQ,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAc,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,EAAE;SAC5F,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC;QACtB,SAAS;IACX;IAEA,gDAAgD;IAChD,IAAI,OAAO,MAAM,KAAK,GAAG;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;AACT;AAOO,SAAS,mBAAmB,OAAY;IAC7C,8BAA8B;IAC9B,IAAI,QAAQ,WAAW,IAAI,MAAM,OAAO,CAAC,QAAQ,WAAW,KAAK,QAAQ,WAAW,CAAC,MAAM,GAAG,GAAG;QAC/F,OAAO,QAAQ,WAAW,CAAC,EAAE;IAC/B;IAEA,+BAA+B;IAC/B,OAAO,QAAQ,WAAW,EAAE,OAAO,CAAC,EAAE,EAAE,MAAM,SAAS;AACzD", "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Benzochem%20Industries%20v2/edit-13%20%28%20clone%20%29/Benzochem-Industries/user/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\nconst DialogPortal = DialogPrimitive.Portal\n\nconst DialogClose = DialogPrimitive.Close\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,qKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,qKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;;;;;;KAVP;AAaN,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,qKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 324, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Benzochem%20Industries%20v2/edit-13%20%28%20clone%20%29/Benzochem-Industries/user/components/product-image-gallery.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport Image from \"next/image\"\nimport { motion, AnimatePresence } from \"framer-motion\"\nimport { ChevronLeft, ChevronRight, Maximize2, X, <PERSON>rk<PERSON> } from \"lucide-react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON>Trigger, DialogHeader, DialogTitle } from \"@/components/ui/dialog\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { getProductImageUrls } from \"@/lib/image-utils\"\nimport type { Product } from \"@/lib/types\"\n\ninterface ProductImageGalleryProps {\n  product: Product\n}\n\nexport default function ProductImageGallery({ product }: ProductImageGalleryProps) {\n  const [currentImageIndex, setCurrentImageIndex] = useState(0)\n  const [isZoomed, setIsZoomed] = useState(false)\n  const [imageError, setImageError] = useState(false)\n\n  // Get all available images from the product using utility function\n  const images = getProductImageUrls(product)\n\n  const nextImage = () => {\n    setCurrentImageIndex((prev) => (prev + 1) % images.length)\n  }\n\n  const prevImage = () => {\n    setCurrentImageIndex((prev) => (prev - 1 + images.length) % images.length)\n  }\n\n  const setImage = (index: number) => {\n    setCurrentImageIndex(index)\n  }\n\n  const getImageUrl = (url: string) => {\n    if (imageError) return \"/placeholder-chemical.jpg\"\n    return url || \"/placeholder-chemical.jpg\"\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      {/* Main Image Display */}\n      <div className=\"relative aspect-square bg-gradient-to-br from-muted/30 to-muted/10 overflow-hidden group\">\n        {/* Quality Badge */}\n        <div className=\"absolute top-4 left-4 z-10\">\n          <Badge className=\"bg-background/95 text-foreground backdrop-blur-sm shadow-sm border border-border/50\">\n            <Sparkles className=\"h-3 w-3 mr-1\" />\n            Premium Quality\n          </Badge>\n        </div>\n\n        {/* Zoom Button */}\n        <Dialog open={isZoomed} onOpenChange={setIsZoomed}>\n          <DialogTrigger asChild>\n            <Button\n              variant=\"secondary\"\n              size=\"icon\"\n              className=\"absolute top-4 right-4 bg-background/95 backdrop-blur-sm z-10 rounded-full shadow-sm hover:bg-background hover:scale-105 transition-all duration-200 border border-border/50\"\n            >\n              <Maximize2 className=\"h-4 w-4\" />\n            </Button>\n          </DialogTrigger>\n          <DialogContent className=\"max-w-6xl w-full h-[90vh] p-0 bg-background/95 backdrop-blur-xl border-border\">\n            <DialogHeader className=\"absolute top-4 left-4 z-50\">\n              <DialogTitle className=\"sr-only\">Product Image - {product.title}</DialogTitle>\n            </DialogHeader>\n            \n            {/* Close Button */}\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"absolute top-4 right-4 z-50 text-foreground hover:bg-muted rounded-full\"\n              onClick={() => setIsZoomed(false)}\n            >\n              <X className=\"h-5 w-5\" />\n            </Button>\n\n            {/* Navigation in Modal */}\n            {images.length > 1 && (\n              <>\n                <Button\n                  variant=\"ghost\"\n                  size=\"icon\"\n                  className=\"absolute left-4 top-1/2 -translate-y-1/2 z-50 text-foreground hover:bg-muted rounded-full\"\n                  onClick={prevImage}\n                >\n                  <ChevronLeft className=\"h-6 w-6\" />\n                </Button>\n                <Button\n                  variant=\"ghost\"\n                  size=\"icon\"\n                  className=\"absolute right-4 top-1/2 -translate-y-1/2 z-50 text-foreground hover:bg-muted rounded-full\"\n                  onClick={nextImage}\n                >\n                  <ChevronRight className=\"h-6 w-6\" />\n                </Button>\n              </>\n            )}\n\n            {/* Full Size Image */}\n            <div className=\"relative w-full h-full flex items-center justify-center p-8\">\n              <div className=\"relative max-w-full max-h-full\">\n                <Image\n                  src={getImageUrl(images[currentImageIndex])}\n                  alt={product.title}\n                  width={800}\n                  height={800}\n                  className=\"object-contain max-w-full max-h-full\"\n                  onError={() => setImageError(true)}\n                />\n              </div>\n            </div>\n\n            {/* Image Counter */}\n            {images.length > 1 && (\n              <div className=\"absolute bottom-4 left-1/2 -translate-x-1/2 z-50\">\n                <Badge className=\"bg-background/90 text-foreground backdrop-blur-sm border border-border/50\">\n                  {currentImageIndex + 1} / {images.length}\n                </Badge>\n              </div>\n            )}\n          </DialogContent>\n        </Dialog>\n\n        {/* Main Image with Animation */}\n        <AnimatePresence mode=\"wait\">\n          <motion.div\n            key={currentImageIndex}\n            initial={{ opacity: 0, scale: 1.05 }}\n            animate={{ opacity: 1, scale: 1 }}\n            exit={{ opacity: 0, scale: 0.95 }}\n            transition={{ duration: 0.4, ease: \"easeInOut\" }}\n            className=\"relative aspect-square\"\n          >\n            <Image\n              src={getImageUrl(images[currentImageIndex])}\n              alt={product.title}\n              fill\n              className=\"object-contain p-8 group-hover:scale-105 transition-transform duration-500\"\n              priority\n              onError={() => setImageError(true)}\n            />\n          </motion.div>\n        </AnimatePresence>\n\n        {/* Navigation Arrows */}\n        {images.length > 1 && (\n          <>\n            <Button\n              variant=\"secondary\"\n              size=\"icon\"\n              className=\"absolute left-3 top-1/2 -translate-y-1/2 bg-background/90 backdrop-blur-sm z-10 rounded-full shadow-sm opacity-0 group-hover:opacity-100 hover:bg-background hover:scale-105 transition-all duration-200 border border-border/50\"\n              onClick={prevImage}\n            >\n              <ChevronLeft className=\"h-5 w-5\" />\n            </Button>\n            <Button\n              variant=\"secondary\"\n              size=\"icon\"\n              className=\"absolute right-3 top-1/2 -translate-y-1/2 bg-background/90 backdrop-blur-sm z-10 rounded-full shadow-sm opacity-0 group-hover:opacity-100 hover:bg-background hover:scale-105 transition-all duration-200 border border-border/50\"\n              onClick={nextImage}\n            >\n              <ChevronRight className=\"h-5 w-5\" />\n            </Button>\n          </>\n        )}\n\n        {/* Image Indicator Dots */}\n        {images.length > 1 && (\n          <div className=\"absolute bottom-4 left-1/2 -translate-x-1/2 z-10\">\n            <div className=\"flex space-x-2 bg-background/90 backdrop-blur-sm rounded-full px-3 py-2 border border-border/50\">\n              {images.map((_, index) => (\n                <button\n                  key={index}\n                  onClick={() => setImage(index)}\n                  className={`w-2 h-2 rounded-full transition-all duration-200 ${\n                    index === currentImageIndex \n                      ? 'bg-primary shadow-sm scale-125' \n                      : 'bg-muted-foreground/50 hover:bg-muted-foreground/75'\n                  }`}\n                />\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Thumbnail Grid */}\n      {images.length > 1 && (\n        <div className=\"grid grid-cols-4 gap-3\">\n          {images.map((image, index) => (\n            <motion.button\n              key={index}\n              onClick={() => setImage(index)}\n              className={`relative aspect-square rounded-xl overflow-hidden border-2 transition-all duration-200 ${\n                index === currentImageIndex \n                  ? 'border-primary shadow-lg scale-105 ring-2 ring-primary/20' \n                  : 'border-border hover:border-primary/50 hover:shadow-md'\n              }`}\n              whileHover={{ scale: index === currentImageIndex ? 1.05 : 1.02 }}\n              whileTap={{ scale: 0.98 }}\n            >\n              <Image \n                src={getImageUrl(image)} \n                alt={`${product.title} - View ${index + 1}`} \n                fill \n                className=\"object-cover\"\n                onError={() => setImageError(true)}\n              />\n              \n              {/* Overlay for non-active thumbnails */}\n              {index !== currentImageIndex && (\n                <div className=\"absolute inset-0 bg-foreground/10 hover:bg-foreground/5 transition-colors duration-200\" />\n              )}\n              \n              {/* Active indicator */}\n              {index === currentImageIndex && (\n                <div className=\"absolute inset-0 ring-2 ring-primary ring-inset rounded-xl\" />\n              )}\n            </motion.button>\n          ))}\n        </div>\n      )}\n\n      {/* Image Info */}\n      <div className=\"text-center space-y-1\">\n        <p className=\"text-xs text-muted-foreground\">\n          Click to zoom \n        </p>\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAgBe,SAAS,oBAAoB,EAAE,OAAO,EAA4B;;IAC/E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,mEAAmE;IACnE,MAAM,SAAS,CAAA,GAAA,wHAAA,CAAA,sBAAmB,AAAD,EAAE;IAEnC,MAAM,YAAY;QAChB,qBAAqB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,OAAO,MAAM;IAC3D;IAEA,MAAM,YAAY;QAChB,qBAAqB,CAAC,OAAS,CAAC,OAAO,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM;IAC3E;IAEA,MAAM,WAAW,CAAC;QAChB,qBAAqB;IACvB;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,YAAY,OAAO;QACvB,OAAO,OAAO;IAChB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,6HAAA,CAAA,QAAK;4BAAC,WAAU;;8CACf,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAMzC,6LAAC,8HAAA,CAAA,SAAM;wBAAC,MAAM;wBAAU,cAAc;;0CACpC,6LAAC,8HAAA,CAAA,gBAAa;gCAAC,OAAO;0CACpB,cAAA,6LAAC,8HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;8CAEV,cAAA,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAGzB,6LAAC,8HAAA,CAAA,gBAAa;gCAAC,WAAU;;kDACvB,6LAAC,8HAAA,CAAA,eAAY;wCAAC,WAAU;kDACtB,cAAA,6LAAC,8HAAA,CAAA,cAAW;4CAAC,WAAU;;gDAAU;gDAAiB,QAAQ,KAAK;;;;;;;;;;;;kDAIjE,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,YAAY;kDAE3B,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;oCAId,OAAO,MAAM,GAAG,mBACf;;0DACE,6LAAC,8HAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS;0DAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;0DAEzB,6LAAC,8HAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS;0DAET,cAAA,6LAAC,yNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;;kDAM9B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAK,YAAY,MAAM,CAAC,kBAAkB;gDAC1C,KAAK,QAAQ,KAAK;gDAClB,OAAO;gDACP,QAAQ;gDACR,WAAU;gDACV,SAAS,IAAM,cAAc;;;;;;;;;;;;;;;;oCAMlC,OAAO,MAAM,GAAG,mBACf,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,6HAAA,CAAA,QAAK;4CAAC,WAAU;;gDACd,oBAAoB;gDAAE;gDAAI,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;;;kCAQlD,6LAAC,4LAAA,CAAA,kBAAe;wBAAC,MAAK;kCACpB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAK;4BACnC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,MAAM;gCAAE,SAAS;gCAAG,OAAO;4BAAK;4BAChC,YAAY;gCAAE,UAAU;gCAAK,MAAM;4BAAY;4BAC/C,WAAU;sCAEV,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAK,YAAY,MAAM,CAAC,kBAAkB;gCAC1C,KAAK,QAAQ,KAAK;gCAClB,IAAI;gCACJ,WAAU;gCACV,QAAQ;gCACR,SAAS,IAAM,cAAc;;;;;;2BAb1B;;;;;;;;;;oBAmBR,OAAO,MAAM,GAAG,mBACf;;0CACE,6LAAC,8HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;0CAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;0CAEzB,6LAAC,8HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;0CAET,cAAA,6LAAC,yNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;oBAM7B,OAAO,MAAM,GAAG,mBACf,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,OAAO,GAAG,CAAC,CAAC,GAAG,sBACd,6LAAC;oCAEC,SAAS,IAAM,SAAS;oCACxB,WAAW,CAAC,iDAAiD,EAC3D,UAAU,oBACN,mCACA,uDACJ;mCANG;;;;;;;;;;;;;;;;;;;;;YAehB,OAAO,MAAM,GAAG,mBACf,6LAAC;gBAAI,WAAU;0BACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wBAEZ,SAAS,IAAM,SAAS;wBACxB,WAAW,CAAC,uFAAuF,EACjG,UAAU,oBACN,8DACA,yDACJ;wBACF,YAAY;4BAAE,OAAO,UAAU,oBAAoB,OAAO;wBAAK;wBAC/D,UAAU;4BAAE,OAAO;wBAAK;;0CAExB,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAK,YAAY;gCACjB,KAAK,GAAG,QAAQ,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG;gCAC3C,IAAI;gCACJ,WAAU;gCACV,SAAS,IAAM,cAAc;;;;;;4BAI9B,UAAU,mCACT,6LAAC;gCAAI,WAAU;;;;;;4BAIhB,UAAU,mCACT,6LAAC;gCAAI,WAAU;;;;;;;uBAzBZ;;;;;;;;;;0BAiCb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BAAgC;;;;;;;;;;;;;;;;;AAMrD;GA1NwB;KAAA", "debugId": null}}, {"offset": {"line": 753, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Benzochem%20Industries%20v2/edit-13%20%28%20clone%20%29/Benzochem-Industries/user/components/animated-background.tsx"], "sourcesContent": ["\"use client\"\n\nimport { motion } from \"framer-motion\"\n\nexport default function AnimatedBackground() {\n  return (\n    <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n      {/* Floating Orbs */}\n      <motion.div\n        className=\"absolute w-64 h-64 bg-primary/5 rounded-full blur-3xl\"\n        animate={{\n          x: [0, 100, 0],\n          y: [0, -50, 0],\n          scale: [1, 1.2, 1],\n        }}\n        transition={{\n          duration: 20,\n          repeat: Infinity,\n          ease: \"easeInOut\",\n        }}\n        style={{ top: \"10%\", left: \"10%\" }}\n      />\n      \n      <motion.div\n        className=\"absolute w-96 h-96 bg-accent/10 rounded-full blur-3xl\"\n        animate={{\n          x: [0, -80, 0],\n          y: [0, 60, 0],\n          scale: [1, 0.8, 1],\n        }}\n        transition={{\n          duration: 25,\n          repeat: Infinity,\n          ease: \"easeInOut\",\n        }}\n        style={{ top: \"60%\", right: \"10%\" }}\n      />\n      \n      <motion.div\n        className=\"absolute w-48 h-48 bg-secondary/20 rounded-full blur-2xl\"\n        animate={{\n          x: [0, 60, 0],\n          y: [0, -40, 0],\n          scale: [1, 1.3, 1],\n        }}\n        transition={{\n          duration: 18,\n          repeat: Infinity,\n          ease: \"easeInOut\",\n        }}\n        style={{ bottom: \"20%\", left: \"20%\" }}\n      />\n\n      {/* Grid Pattern */}\n      <div className=\"absolute inset-0 opacity-[0.02]\">\n        <div \n          className=\"w-full h-full\"\n          style={{\n            backgroundImage: `\n              linear-gradient(rgba(var(--foreground), 0.1) 1px, transparent 1px),\n              linear-gradient(90deg, rgba(var(--foreground), 0.1) 1px, transparent 1px)\n            `,\n            backgroundSize: '50px 50px'\n          }}\n        />\n      </div>\n\n      {/* Gradient Overlays */}\n      <div className=\"absolute top-0 left-0 w-full h-32 bg-gradient-to-b from-background/80 to-transparent\" />\n      <div className=\"absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-background/80 to-transparent\" />\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG;wBAAC;wBAAG;wBAAK;qBAAE;oBACd,GAAG;wBAAC;wBAAG,CAAC;wBAAI;qBAAE;oBACd,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;gBACpB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;gBACA,OAAO;oBAAE,KAAK;oBAAO,MAAM;gBAAM;;;;;;0BAGnC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG;wBAAC;wBAAG,CAAC;wBAAI;qBAAE;oBACd,GAAG;wBAAC;wBAAG;wBAAI;qBAAE;oBACb,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;gBACpB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;gBACA,OAAO;oBAAE,KAAK;oBAAO,OAAO;gBAAM;;;;;;0BAGpC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG;wBAAC;wBAAG;wBAAI;qBAAE;oBACb,GAAG;wBAAC;wBAAG,CAAC;wBAAI;qBAAE;oBACd,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;gBACpB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;gBACA,OAAO;oBAAE,QAAQ;oBAAO,MAAM;gBAAM;;;;;;0BAItC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBACL,iBAAiB,CAAC;;;YAGlB,CAAC;wBACD,gBAAgB;oBAClB;;;;;;;;;;;0BAKJ,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;KApEwB", "debugId": null}}, {"offset": {"line": 918, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Benzochem%20Industries%20v2/edit-13%20%28%20clone%20%29/Benzochem-Industries/user/components/product-detail.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useSecureQuotation } from \"@/hooks/use-secure-quotation\";\nimport { motion } from \"framer-motion\";\nimport {\n  ShoppingBag,\n  Heart,\n  Share2,\n  Download,\n  ChevronDown,\n  ChevronUp,\n  Award,\n  Shield,\n  Truck,\n  CheckCircle,\n  Info,\n  Package,\n  Sparkles,\n  Target,\n  Beaker,\n  Factory,\n  BookmarkPlus,\n  ExternalLink\n} from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\"\nimport AddToQuotationButton from \"@/components/add-to-quotation-button\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { getProductImageUrl, getProductCategory } from \"@/lib/image-utils\";\nimport type { Product, ProductVariant } from \"@/lib/types\";\nimport ProductImageGallery from \"@/components/product-image-gallery\";\nimport AnimatedBackground from \"@/components/animated-background\";\n\ninterface ProductDetailProps {\n  product: Product;\n}\n\nexport default function ProductDetail({ product }: ProductDetailProps) {\n  const [quantity, setQuantity] = useState(1);\n  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | undefined>(undefined);\n  const [packageSize, setPackageSize] = useState(\"1 KG\");\n  const [isSaved, setIsSaved] = useState(false);\n  const { addToQuotation, isLoading: isQuotationLoading } = useSecureQuotation();\n  // const { toast } = useToast();\n\n  \n  useEffect(() => {\n    // Initialize selectedVariant with the first available variant\n    if (product.variants?.edges && product.variants.edges.length > 0) {\n      const firstAvailableVariant = product.variants.edges.find(edge => edge.node.availableForSale)?.node;\n      setSelectedVariant(firstAvailableVariant || product.variants.edges[0].node);\n    }\n  }, [product.variants]);\n\n  const handleVariantChange = (event: React.ChangeEvent<HTMLSelectElement>) => {\n    const variantId = event.target.value;\n    const newSelectedVariant = product.variants?.edges?.find(\n      (edge) => edge.node.id === variantId\n    )?.node;\n    setSelectedVariant(newSelectedVariant);\n  };\n\n  // Handle package size change\n  const handlePackageSizeChange = (event: React.ChangeEvent<HTMLSelectElement>) => {\n    setPackageSize(event.target.value);\n  };\n\n  // Get available package sizes from product metafields or use defaults\n  const getAvailablePackageSizes = () => {\n    const packageSizesMetafield = getMetafieldValue(\"package_sizes\");\n    if (packageSizesMetafield) {\n      try {\n        const sizes = JSON.parse(packageSizesMetafield);\n        if (Array.isArray(sizes) && sizes.length > 0) {\n          return sizes;\n        }\n      } catch (error) {\n        console.warn(\"Failed to parse package sizes metafield:\", error);\n      }\n    }\n    // Default package sizes for chemical products\n    return [\"1 KG\", \"2 KG\", \"5 KG\", \"10 KG\", \"25 KG\"];\n  };\n\n  // Handle save/bookmark functionality\n  const handleSaveProduct = () => {\n    setIsSaved(!isSaved);\n    console.log(isSaved ? \"Removed from saved products\" : \"Added to saved products\");\n  };\n\n  // Handle share functionality\n  const handleShareProduct = async () => {\n    if (navigator.share) {\n      try {\n        await navigator.share({\n          title: product.title,\n          text: `Check out this product: ${product.title}`,\n          url: window.location.href,\n        });\n      } catch (error) {\n        console.log(\"Error sharing:\", error);\n        handleCopyLink();\n      }\n    } else {\n      handleCopyLink();\n    }\n  };\n\n  // Fallback copy link functionality\n  const handleCopyLink = () => {\n    navigator.clipboard.writeText(window.location.href).then(() => {\n      console.log(\"Link copied to clipboard\");\n    }).catch(() => {\n      console.error(\"Failed to copy link\");\n    });\n  };\n\n  // Handle document download\n  const handleDownloadSpecs = () => {\n    const specs = {\n      productName: product.title,\n      casNumber: getMetafieldValue(\"cas_number\"),\n      molecularFormula: getMolecularFormula(),\n      purity: getMetafieldValue(\"purity\"),\n      hsnNumber: getMetafieldValue(\"hsn_number\"),\n      description: product.description,\n      specifications: keySpecs.map(spec => ({ [spec.label]: spec.value }))\n    };\n    \n    const dataStr = JSON.stringify(specs, null, 2);\n    const dataBlob = new Blob([dataStr], { type: 'application/json' });\n    const url = URL.createObjectURL(dataBlob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `${product.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_specifications.json`;\n    link.click();\n    URL.revokeObjectURL(url);\n  };\n\n  // Helper function to check if product is available for sale\n  const isProductAvailable = () => {\n    // If product has variants, check variant availability\n    if (product.variants?.edges && product.variants.edges.length > 0) {\n      // Check variant availability first\n      if (!selectedVariant?.availableForSale) {\n        return false;\n      }\n      \n      // Check variant quantity (from Shopify/external system)\n      if (selectedVariant.quantityAvailable !== null && \n          selectedVariant.quantityAvailable !== undefined && \n          selectedVariant.quantityAvailable <= 0) {\n        return false;\n      }\n    }\n    \n    // Check product quantity (from admin-managed inventory)\n    if (product.quantity !== undefined && product.quantity <= 0) {\n      return false;\n    }\n    \n    return true;\n  };\n\n  // Helper function to get available stock count\n  const getAvailableStock = () => {\n    // Prioritize product quantity (admin-managed) over variant quantity\n    if (product.quantity !== undefined && product.quantity !== null) {\n      return product.quantity;\n    }\n    \n    // Fallback to variant quantity\n    if (selectedVariant?.quantityAvailable !== null && \n        selectedVariant?.quantityAvailable !== undefined) {\n      return selectedVariant.quantityAvailable;\n    }\n    \n    // Unknown stock level\n    return null;\n  };\n\n  // Helper function to check if stock is low\n  const isLowStock = () => {\n    const stock = getAvailableStock();\n    return stock !== null && stock > 0 && stock < 10;\n  };\n\n  // Helper function to get stock status message\n  const getStockStatus = () => {\n    if (!isProductAvailable()) {\n      return \"Out of Stock\";\n    }\n    \n    const stock = getAvailableStock();\n    if (isLowStock() && stock !== null) {\n      return `Low in stock`;\n    }\n    \n    return \"In Stock\";\n  };\n\n  // Helper function to get stock badge color\n  const getStockBadgeColor = () => {\n    if (!isProductAvailable()) {\n      return \"bg-red-50 text-red-700 border-red-200\";\n    }\n    \n    if (isLowStock()) {\n      return \"bg-yellow-50 text-yellow-700 border-yellow-200\";\n    }\n    \n    return \"bg-green-50 text-green-700 border-green-200\";\n  };\n\n  \n  const handleAddToQuotation = async () => {\n    // Check if product has variants and if so, ensure one is selected\n    if (product.variants?.edges && product.variants.edges.length > 0 && !selectedVariant) {\n      console.error(\"No variant selected or available\");\n      return;\n    }\n    \n    if (!isProductAvailable()) {\n      console.error(\"Product is not available for sale\");\n      return;\n    }\n\n    // Check if requested quantity exceeds available stock\n    const availableStock = getAvailableStock();\n    if (availableStock !== null && quantity > availableStock) {\n      console.error(`Requested quantity (${quantity}) exceeds available stock (${availableStock})`);\n      return;\n    }\n\n    const itemToAdd = {\n      productId: product.id,\n      variantId: selectedVariant?.id || `${product.id}-default`,\n      name: product.title,\n      category: getCategory(),\n      image: getProductImageUrl(product),\n      price: parseFloat(selectedVariant?.priceV2?.amount || product.priceRange?.minVariantPrice?.amount || \"0\"),\n      quantity: quantity,\n      unit: packageSize,\n      specifications: `Package Size: ${packageSize}`,\n    };\n\n    console.log(\"Attempting to add to quotation:\", itemToAdd);\n\n    try {\n      await addToQuotation(itemToAdd);\n      console.log(`${product.title} ${selectedVariant ? `(${selectedVariant.title})` : ''} added to quotation`);\n    } catch (error) {\n      console.error(\"Failed to add item to quotation:\", error);\n    }\n  };\n\n  const incrementQuantity = () => {\n    const availableStock = getAvailableStock();\n    setQuantity((prev) => {\n      // If we know the stock level, don't exceed it\n      if (availableStock !== null && prev >= availableStock) {\n        return prev; // Don't increment if at stock limit\n      }\n      return prev + 1;\n    });\n  };\n\n  const decrementQuantity = () => {\n    if (quantity > 1) {\n      setQuantity((prev) => prev - 1);\n    }\n  };\n\n  const getCategory = () => {\n    return getProductCategory(product);\n  };\n\n  // Helper function to get metafield value\n  const getMetafieldValue = (key: string) => {\n    if (!product.metafields || !Array.isArray(product.metafields)) {\n      return \"\";\n    }\n    return (\n      product.metafields.find((field) => field && field.key === key)?.value ||\n      \"\"\n    );\n  };\n\n  // Helper function to safely parse molecular formula\n  const getMolecularFormula = () => {\n    try {\n      const formula = getMetafieldValue(\"molecular_formula\");\n      if (!formula) return \"N/A\";\n      \n      // Try to parse as JSON (Shopify format)\n      const parsed = JSON.parse(formula);\n      if (parsed.children && parsed.children[0] && parsed.children[0].children && parsed.children[0].children[0]) {\n        return parsed.children[0].children[0].value;\n      }\n      return formula; // Return as-is if not in expected format\n    } catch (error) {\n      // If JSON parsing fails, return the raw value or a default\n      const formula = getMetafieldValue(\"molecular_formula\");\n      return formula || \"N/A\";\n    }\n  };\n\n  // Product stats for the hero section\n  const productStats = [\n    {\n      icon: Award,\n      value: `${getMetafieldValue(\"purity\") || \"99.9\"}%`,\n      label: \"Purity\",\n      description: \"Guaranteed quality\"\n    },\n    {\n      icon: Shield,\n      value: getMetafieldValue(\"cas_number\") ? \"CAS\" : \"N/A\",\n      label: \"CAS Verified\", \n      description: \"Certified standard\"\n    },\n    {\n      icon: Factory,\n      value: \"ISO\",\n      label: \"Certified\",\n      description: \"Quality assured\"\n    },\n    {\n      icon: Truck,\n      value: getStockStatus(),\n      label: \"Availability\",\n      description: \"Ready to ship\"\n    }\n  ];\n\n  // Product features/benefits\n  const productFeatures = [\n    {\n      icon: Beaker,\n      title: \"High Purity\",\n      description: `${getMetafieldValue(\"purity\") || \"99.9\"}% pure chemical composition with rigorous quality control.`\n    },\n    {\n      icon: Shield,\n      title: \"Quality Assured\",\n      description: \"All products meet international standards and undergo comprehensive testing.\"\n    },\n    {\n      icon: Package,\n      title: \"Secure Packaging\",\n      description: \"Professional-grade packaging ensures product integrity during shipping.\"\n    },\n    {\n      icon: Target,\n      title: \"Application Ready\",\n      description: \"Optimized for industrial, pharmaceutical, and research applications.\"\n    }\n  ];\n\n  // Key specifications for cards\n  const keySpecs = [\n    {\n      label: \"Molecular Formula\",\n      value: getMolecularFormula(),\n      icon: Beaker\n    },\n    {\n      label: \"CAS Number\", \n      value: getMetafieldValue(\"cas_number\") || \"N/A\",\n      icon: Info\n    },\n    {\n      label: \"HSN Number\",\n      value: getMetafieldValue(\"hsn_number\"),\n      icon: Package\n    },\n    {\n      label: \"Purity Level\",\n      value: `${getMetafieldValue(\"purity\") || \"99.9\"}%`,\n      icon: Award\n    }\n  ];\n\n  return (\n    <div className=\"relative\">\n      {/* Background Elements */}\n      <AnimatedBackground />\n      <div className=\"absolute inset-0 bg-gradient-to-br from-background via-background to-secondary/10\" />\n      \n      <div className=\"container mx-auto px-4 py-8 relative z-10\">\n        {/* Product Hero Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"text-center mb-16\"\n        >\n          <div className=\"flex items-center justify-center gap-2 mb-4\">\n            <Badge className=\"bg-primary/10 text-primary border-primary/20 hover:bg-primary/20 transition-colors\">\n              <Package className=\"h-3 w-3 mr-1\" />\n              {getCategory()}\n            </Badge>\n            <Badge \n              className={`${getStockBadgeColor()} border transition-colors`}\n            >\n              <CheckCircle className=\"h-3 w-3 mr-1\" />\n              {getStockStatus()}\n            </Badge>\n          </div>\n          \n          <h1 className=\"text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent leading-tight\">\n            {product.title}\n          </h1>\n          \n          <div className=\"max-w-3xl mx-auto mb-8\">\n            <p \n              className=\"text-lg md:text-xl text-muted-foreground leading-relaxed\" \n              dangerouslySetInnerHTML={{ __html: product.descriptionHtml || product.description }}\n            />\n          </div>\n\n          {isProductAvailable() && isLowStock() && (\n            <motion.div\n              initial={{ opacity: 0, scale: 0.9 }}\n              animate={{ opacity: 1, scale: 1 }}\n              className=\"inline-flex items-center gap-2 bg-amber-50 text-amber-700 px-4 py-2 rounded-full text-sm font-medium border border-amber-200\"\n            >\n              <Sparkles className=\"h-4 w-4\" />\n              Hurry! Only {getAvailableStock()} units left\n            </motion.div>\n          )}\n        </motion.div>\n\n        {/* Product Stats */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-20\"\n        >\n          {productStats.map((stat, index) => (\n            <Card key={index} className=\"border-0 bg-card/50 backdrop-blur-sm hover:bg-card/80 transition-all duration-300 hover:shadow-lg\">\n              <CardContent className=\"p-6 text-center\">\n                <div className=\"w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <stat.icon className=\"h-6 w-6 text-primary\" />\n                </div>\n                <div className=\"text-2xl font-bold text-foreground mb-1\">{stat.value}</div>\n                <div className=\"text-sm font-medium text-foreground mb-1\">{stat.label}</div>\n                <div className=\"text-xs text-muted-foreground\">{stat.description}</div>\n              </CardContent>\n            </Card>\n          ))}\n        </motion.div>\n\n        {/* Main Product Content */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 items-start mb-20\">\n          {/* Image Gallery Side */}\n          <motion.div\n            initial={{ opacity: 0, x: -30 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.6, delay: 0.3 }}\n            className=\"space-y-8\"\n          >\n            <Card className=\"overflow-hidden border-0\">\n              <ProductImageGallery product={product} />\n            </Card>\n\n            {/* Product Features */}\n            <Card className=\"border-0 bg-card/50 backdrop-blur-sm\">\n              <CardContent className=\"p-6\">\n                <h3 className=\"text-xl font-semibold mb-6 flex items-center gap-2\">\n                  <Sparkles className=\"h-5 w-5 text-primary\" />\n                  Product Features\n                </h3>\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\n                  {productFeatures.map((feature, index) => (\n                    <motion.div\n                      key={index}\n                      initial={{ opacity: 0, y: 20 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      transition={{ duration: 0.4, delay: 0.4 + index * 0.1 }}\n                      className=\"flex items-start space-x-3 p-4 rounded-xl bg-muted/30 hover:bg-muted/50 transition-colors\"\n                    >\n                      <div className=\"w-8 h-8 bg-primary/10 rounded-xl flex items-center justify-center flex-shrink-0\">\n                        <feature.icon className=\"h-4 w-4 text-primary\" />\n                      </div>\n                      <div className=\"flex-1\">\n                        <h4 className=\"font-medium text-foreground mb-1\">{feature.title}</h4>\n                        <p className=\"text-xs text-muted-foreground leading-relaxed\">{feature.description}</p>\n                      </div>\n                    </motion.div>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n          </motion.div>\n\n          {/* Product Info Side */}\n          <motion.div\n            initial={{ opacity: 0, x: 30 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.6, delay: 0.4 }}\n            className=\"space-y-8\"\n          >\n            {/* Specifications */}\n            <Card className=\"border-0 bg-card/50 backdrop-blur-sm\">\n              <CardContent className=\"p-6\">\n                <h3 className=\"text-xl font-semibold mb-6 flex items-center gap-2\">\n                  <Info className=\"h-5 w-5 text-primary\" />\n                  Technical Specifications\n                </h3>\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\n                  {keySpecs.map((spec, index) => (\n                    <div key={index} className=\"flex items-center justify-between p-4 rounded-xl bg-muted/30\">\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"w-8 h-8 bg-primary/10 rounded-xl flex items-center justify-center\">\n                          <spec.icon className=\"h-4 w-4 text-primary\" />\n                        </div>\n                        <div>\n                          <p className=\"text-xs text-muted-foreground\">{spec.label}</p>\n                          <p className=\"font-medium text-foreground\">{spec.value}</p>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Purchase Section */}\n            <Card className=\"border-0 bg-gradient-to-r from-primary/5 to-accent/5 backdrop-blur-sm\">\n              <CardContent className=\"p-6 space-y-6\">\n                <h3 className=\"text-xl font-semibold flex items-center gap-2\">\n                  <ShoppingBag className=\"h-5 w-5 text-primary\" />\n                  Order Details\n                </h3>\n                \n                {/* Variant Selection */}\n                {product.variants?.edges && product.variants.edges.length > 1 && (\n                  <div>\n                    <label className=\"text-sm font-medium text-muted-foreground mb-3 flex items-center gap-2\">\n                      <Package className=\"h-4 w-4\" />\n                      Product Variant\n                    </label>\n                    <select \n                      className=\"w-full h-12 px-4 border rounded-xl bg-background/50 backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50 transition-all\"\n                      value={selectedVariant?.id || \"\"}\n                      onChange={handleVariantChange}\n                    >\n                      {product.variants.edges.map((edge) => (\n                        <option key={edge.node.id} value={edge.node.id}>\n                          {edge.node.title} - ${edge.node.priceV2.amount} {edge.node.priceV2.currencyCode}\n                          {!edge.node.availableForSale && \" (Out of Stock)\"}\n                        </option>\n                      ))}\n                    </select>\n                  </div>\n                )}\n\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"text-sm font-medium text-muted-foreground mb-3 flex items-center gap-2\">\n                      <Package className=\"h-4 w-4\" />\n                      Quantity\n                      {getAvailableStock() !== null && (\n                        <span className=\"text-xs text-muted-foreground\">\n                          (Max: {getAvailableStock()})\n                        </span>\n                      )}\n                    </label>\n                    <div className=\"flex items-center border rounded-xl bg-background/50 backdrop-blur-sm\">\n                      <Button\n                        variant=\"ghost\"\n                        size=\"icon\"\n                        className=\"h-12 w-12 rounded-l-xl rounded-r-none\"\n                        onClick={decrementQuantity}\n                        disabled={quantity <= 1}\n                      >\n                        <ChevronDown className=\"h-4 w-4\" />\n                      </Button>\n                      <span className=\"flex-1 text-center font-medium py-3\">{quantity}</span>\n                      <Button\n                        variant=\"ghost\"\n                        size=\"icon\"\n                        className=\"h-12 w-12 rounded-r-xl rounded-l-none\"\n                        onClick={incrementQuantity}\n                        disabled={getAvailableStock() !== null && quantity >= getAvailableStock()!}\n                      >\n                        <ChevronUp className=\"h-4 w-4\" />\n                      </Button>\n                    </div>\n                    {getAvailableStock() !== null && quantity > getAvailableStock()! && (\n                      <p className=\"text-xs text-red-600 mt-2\">\n                        Quantity exceeds available stock\n                      </p>\n                    )}\n                  </div>\n\n                  <div>\n                    <label className=\"text-sm font-medium text-muted-foreground mb-3 flex items-center gap-2\">\n                      <Package className=\"h-4 w-4\" />\n                      Package Size\n                    </label>\n                    <select \n                      className=\"w-full h-12 px-4 border rounded-xl bg-background/50 backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50 transition-all\"\n                      value={packageSize}\n                      onChange={handlePackageSizeChange}\n                    >\n                      {getAvailablePackageSizes().map((size) => (\n                        <option key={size} value={size}>\n                          {size}\n                        </option>\n                      ))}\n                    </select>\n                  </div>\n                </div>\n\n                <AddToQuotationButton\n                  productId={product.id}\n                  variantId={selectedVariant?.id || `${product.id}-default`}\n                  name={product.title}\n                  price={parseFloat(selectedVariant?.priceV2?.amount || product.priceRange?.minVariantPrice?.amount || \"0\")}\n                  quantity={quantity}\n                  image={(() => {\n                    // Handle new admin API format\n                    if (product.images && Array.isArray(product.images) && product.images.length > 0) {\n                      const firstImage = product.images[0]\n                      return typeof firstImage === 'string' ? firstImage : firstImage.url\n                    }\n                    // Handle legacy Shopify format\n                    return product.images?.edges?.[0]?.node?.url || \n                           product.media?.edges?.[0]?.node?.image?.url || \n                           product.featuredMedia?.preview?.image?.url || \"\"\n                  })()}\n                  category={getCategory()}\n                  packageSize={packageSize}\n                  disabled={!isProductAvailable() || (product.variants?.edges && product.variants.edges.length > 0 && !selectedVariant)}\n                  className=\"w-full h-14 text-base font-medium\"\n                />\n                \n                <div className=\"grid grid-cols-2 gap-3\">\n                  <Button \n                    variant=\"outline\" \n                    size=\"lg\" \n                    className=\"flex-1 h-12\"\n                    onClick={handleSaveProduct}\n                  >\n                    {isSaved ? (\n                      <BookmarkPlus className=\"h-4 w-4 mr-2 fill-current\" />\n                    ) : (\n                      <Heart className=\"h-4 w-4 mr-2\" />\n                    )}\n                    {isSaved ? \"Saved\" : \"Save\"}\n                  </Button>\n                  <Button \n                    variant=\"outline\" \n                    size=\"lg\" \n                    className=\"flex-1 h-12\"\n                    onClick={handleShareProduct}\n                  >\n                    <Share2 className=\"h-4 w-4 mr-2\" />\n                    Share\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Trust Indicators */}\n            <div className=\"grid grid-cols-3 gap-4\">\n              {[\n                { icon: Shield, text: \"Quality Guaranteed\" },\n                { icon: Truck, text: \"Fast Shipping\" },\n                { icon: Award, text: \"Certified Pure\" }\n              ].map((indicator, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.4, delay: 0.6 + index * 0.1 }}\n                  className=\"flex flex-col items-center text-center p-4 rounded-xl bg-muted/30 hover:bg-muted/50 transition-colors\"\n                >\n                  <indicator.icon className=\"h-6 w-6 text-primary mb-2\" />\n                  <span className=\"text-xs text-muted-foreground\">{indicator.text}</span>\n                </motion.div>\n              ))}\n            </div>\n          </motion.div>\n        </div>\n\n        {/* Download Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.8 }}\n        >\n          <Card className=\"border-0 bg-gradient-to-r from-primary/5 to-accent/5 backdrop-blur-sm\">\n            <CardContent className=\"p-8 text-center\">\n              <h3 className=\"text-2xl font-semibold mb-4 text-foreground\">Technical Documentation</h3>\n              <p className=\"text-muted-foreground mb-6 max-w-2xl mx-auto\">\n                Access detailed specifications, safety data sheets, and technical documents for this product.\n              </p>\n              <Button className=\"group\" onClick={handleDownloadSpecs}>\n                <Download className=\"mr-2 h-4 w-4 transition-transform group-hover:translate-y-1\" />\n                Download Complete Specs\n              </Button>\n            </CardContent>\n          </Card>\n        </motion.div>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoBA;AACA;AACA;AACA;AACA;AAEA;AACA;;;AAhCA;;;;;;;;;;;;AAsCe,SAAS,cAAc,EAAE,OAAO,EAAsB;;IACnE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B;IACnF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,cAAc,EAAE,WAAW,kBAAkB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,qBAAkB,AAAD;IAC3E,gCAAgC;IAGhC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,8DAA8D;YAC9D,IAAI,QAAQ,QAAQ,EAAE,SAAS,QAAQ,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG;gBAChE,MAAM,wBAAwB,QAAQ,QAAQ,CAAC,KAAK,CAAC,IAAI;+CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,gBAAgB;+CAAG;gBAC/F,mBAAmB,yBAAyB,QAAQ,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI;YAC5E;QACF;kCAAG;QAAC,QAAQ,QAAQ;KAAC;IAErB,MAAM,sBAAsB,CAAC;QAC3B,MAAM,YAAY,MAAM,MAAM,CAAC,KAAK;QACpC,MAAM,qBAAqB,QAAQ,QAAQ,EAAE,OAAO,KAClD,CAAC,OAAS,KAAK,IAAI,CAAC,EAAE,KAAK,YAC1B;QACH,mBAAmB;IACrB;IAEA,6BAA6B;IAC7B,MAAM,0BAA0B,CAAC;QAC/B,eAAe,MAAM,MAAM,CAAC,KAAK;IACnC;IAEA,sEAAsE;IACtE,MAAM,2BAA2B;QAC/B,MAAM,wBAAwB,kBAAkB;QAChD,IAAI,uBAAuB;YACzB,IAAI;gBACF,MAAM,QAAQ,KAAK,KAAK,CAAC;gBACzB,IAAI,MAAM,OAAO,CAAC,UAAU,MAAM,MAAM,GAAG,GAAG;oBAC5C,OAAO;gBACT;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,4CAA4C;YAC3D;QACF;QACA,8CAA8C;QAC9C,OAAO;YAAC;YAAQ;YAAQ;YAAQ;YAAS;SAAQ;IACnD;IAEA,qCAAqC;IACrC,MAAM,oBAAoB;QACxB,WAAW,CAAC;QACZ,QAAQ,GAAG,CAAC,UAAU,gCAAgC;IACxD;IAEA,6BAA6B;IAC7B,MAAM,qBAAqB;QACzB,IAAI,UAAU,KAAK,EAAE;YACnB,IAAI;gBACF,MAAM,UAAU,KAAK,CAAC;oBACpB,OAAO,QAAQ,KAAK;oBACpB,MAAM,CAAC,wBAAwB,EAAE,QAAQ,KAAK,EAAE;oBAChD,KAAK,OAAO,QAAQ,CAAC,IAAI;gBAC3B;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC,kBAAkB;gBAC9B;YACF;QACF,OAAO;YACL;QACF;IACF;IAEA,mCAAmC;IACnC,MAAM,iBAAiB;QACrB,UAAU,SAAS,CAAC,SAAS,CAAC,OAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC;YACvD,QAAQ,GAAG,CAAC;QACd,GAAG,KAAK,CAAC;YACP,QAAQ,KAAK,CAAC;QAChB;IACF;IAEA,2BAA2B;IAC3B,MAAM,sBAAsB;QAC1B,MAAM,QAAQ;YACZ,aAAa,QAAQ,KAAK;YAC1B,WAAW,kBAAkB;YAC7B,kBAAkB;YAClB,QAAQ,kBAAkB;YAC1B,WAAW,kBAAkB;YAC7B,aAAa,QAAQ,WAAW;YAChC,gBAAgB,SAAS,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAAE,CAAC,KAAK,KAAK,CAAC,EAAE,KAAK,KAAK;gBAAC,CAAC;QACpE;QAEA,MAAM,UAAU,KAAK,SAAS,CAAC,OAAO,MAAM;QAC5C,MAAM,WAAW,IAAI,KAAK;YAAC;SAAQ,EAAE;YAAE,MAAM;QAAmB;QAChE,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG,GAAG,QAAQ,KAAK,CAAC,OAAO,CAAC,eAAe,KAAK,WAAW,GAAG,oBAAoB,CAAC;QAChG,KAAK,KAAK;QACV,IAAI,eAAe,CAAC;IACtB;IAEA,4DAA4D;IAC5D,MAAM,qBAAqB;QACzB,sDAAsD;QACtD,IAAI,QAAQ,QAAQ,EAAE,SAAS,QAAQ,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG;YAChE,mCAAmC;YACnC,IAAI,CAAC,iBAAiB,kBAAkB;gBACtC,OAAO;YACT;YAEA,wDAAwD;YACxD,IAAI,gBAAgB,iBAAiB,KAAK,QACtC,gBAAgB,iBAAiB,KAAK,aACtC,gBAAgB,iBAAiB,IAAI,GAAG;gBAC1C,OAAO;YACT;QACF;QAEA,wDAAwD;QACxD,IAAI,QAAQ,QAAQ,KAAK,aAAa,QAAQ,QAAQ,IAAI,GAAG;YAC3D,OAAO;QACT;QAEA,OAAO;IACT;IAEA,+CAA+C;IAC/C,MAAM,oBAAoB;QACxB,oEAAoE;QACpE,IAAI,QAAQ,QAAQ,KAAK,aAAa,QAAQ,QAAQ,KAAK,MAAM;YAC/D,OAAO,QAAQ,QAAQ;QACzB;QAEA,+BAA+B;QAC/B,IAAI,iBAAiB,sBAAsB,QACvC,iBAAiB,sBAAsB,WAAW;YACpD,OAAO,gBAAgB,iBAAiB;QAC1C;QAEA,sBAAsB;QACtB,OAAO;IACT;IAEA,2CAA2C;IAC3C,MAAM,aAAa;QACjB,MAAM,QAAQ;QACd,OAAO,UAAU,QAAQ,QAAQ,KAAK,QAAQ;IAChD;IAEA,8CAA8C;IAC9C,MAAM,iBAAiB;QACrB,IAAI,CAAC,sBAAsB;YACzB,OAAO;QACT;QAEA,MAAM,QAAQ;QACd,IAAI,gBAAgB,UAAU,MAAM;YAClC,OAAO,CAAC,YAAY,CAAC;QACvB;QAEA,OAAO;IACT;IAEA,2CAA2C;IAC3C,MAAM,qBAAqB;QACzB,IAAI,CAAC,sBAAsB;YACzB,OAAO;QACT;QAEA,IAAI,cAAc;YAChB,OAAO;QACT;QAEA,OAAO;IACT;IAGA,MAAM,uBAAuB;QAC3B,kEAAkE;QAClE,IAAI,QAAQ,QAAQ,EAAE,SAAS,QAAQ,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,iBAAiB;YACpF,QAAQ,KAAK,CAAC;YACd;QACF;QAEA,IAAI,CAAC,sBAAsB;YACzB,QAAQ,KAAK,CAAC;YACd;QACF;QAEA,sDAAsD;QACtD,MAAM,iBAAiB;QACvB,IAAI,mBAAmB,QAAQ,WAAW,gBAAgB;YACxD,QAAQ,KAAK,CAAC,CAAC,oBAAoB,EAAE,SAAS,2BAA2B,EAAE,eAAe,CAAC,CAAC;YAC5F;QACF;QAEA,MAAM,YAAY;YAChB,WAAW,QAAQ,EAAE;YACrB,WAAW,iBAAiB,MAAM,GAAG,QAAQ,EAAE,CAAC,QAAQ,CAAC;YACzD,MAAM,QAAQ,KAAK;YACnB,UAAU;YACV,OAAO,CAAA,GAAA,wHAAA,CAAA,qBAAkB,AAAD,EAAE;YAC1B,OAAO,WAAW,iBAAiB,SAAS,UAAU,QAAQ,UAAU,EAAE,iBAAiB,UAAU;YACrG,UAAU;YACV,MAAM;YACN,gBAAgB,CAAC,cAAc,EAAE,aAAa;QAChD;QAEA,QAAQ,GAAG,CAAC,mCAAmC;QAE/C,IAAI;YACF,MAAM,eAAe;YACrB,QAAQ,GAAG,CAAC,GAAG,QAAQ,KAAK,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC,EAAE,gBAAgB,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,mBAAmB,CAAC;QAC1G,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD;IACF;IAEA,MAAM,oBAAoB;QACxB,MAAM,iBAAiB;QACvB,YAAY,CAAC;YACX,8CAA8C;YAC9C,IAAI,mBAAmB,QAAQ,QAAQ,gBAAgB;gBACrD,OAAO,MAAM,oCAAoC;YACnD;YACA,OAAO,OAAO;QAChB;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,WAAW,GAAG;YAChB,YAAY,CAAC,OAAS,OAAO;QAC/B;IACF;IAEA,MAAM,cAAc;QAClB,OAAO,CAAA,GAAA,wHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yCAAyC;IACzC,MAAM,oBAAoB,CAAC;QACzB,IAAI,CAAC,QAAQ,UAAU,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ,UAAU,GAAG;YAC7D,OAAO;QACT;QACA,OACE,QAAQ,UAAU,CAAC,IAAI,CAAC,CAAC,QAAU,SAAS,MAAM,GAAG,KAAK,MAAM,SAChE;IAEJ;IAEA,oDAAoD;IACpD,MAAM,sBAAsB;QAC1B,IAAI;YACF,MAAM,UAAU,kBAAkB;YAClC,IAAI,CAAC,SAAS,OAAO;YAErB,wCAAwC;YACxC,MAAM,SAAS,KAAK,KAAK,CAAC;YAC1B,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,CAAC,EAAE,IAAI,OAAO,QAAQ,CAAC,EAAE,CAAC,QAAQ,IAAI,OAAO,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE,EAAE;gBAC1G,OAAO,OAAO,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,KAAK;YAC7C;YACA,OAAO,SAAS,yCAAyC;QAC3D,EAAE,OAAO,OAAO;YACd,2DAA2D;YAC3D,MAAM,UAAU,kBAAkB;YAClC,OAAO,WAAW;QACpB;IACF;IAEA,qCAAqC;IACrC,MAAM,eAAe;QACnB;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO,GAAG,kBAAkB,aAAa,OAAO,CAAC,CAAC;YAClD,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,yMAAA,CAAA,SAAM;YACZ,OAAO,kBAAkB,gBAAgB,QAAQ;YACjD,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,2MAAA,CAAA,UAAO;YACb,OAAO;YACP,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,OAAO;YACP,aAAa;QACf;KACD;IAED,4BAA4B;IAC5B,MAAM,kBAAkB;QACtB;YACE,MAAM,yMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa,GAAG,kBAAkB,aAAa,OAAO,0DAA0D,CAAC;QACnH;QACA;YACE,MAAM,yMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,2MAAA,CAAA,UAAO;YACb,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,yMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;QACf;KACD;IAED,+BAA+B;IAC/B,MAAM,WAAW;QACf;YACE,OAAO;YACP,OAAO;YACP,MAAM,yMAAA,CAAA,SAAM;QACd;QACA;YACE,OAAO;YACP,OAAO,kBAAkB,iBAAiB;YAC1C,MAAM,qMAAA,CAAA,OAAI;QACZ;QACA;YACE,OAAO;YACP,OAAO,kBAAkB;YACzB,MAAM,2MAAA,CAAA,UAAO;QACf;QACA;YACE,OAAO;YACP,OAAO,GAAG,kBAAkB,aAAa,OAAO,CAAC,CAAC;YAClD,MAAM,uMAAA,CAAA,QAAK;QACb;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,wIAAA,CAAA,UAAkB;;;;;0BACnB,6LAAC;gBAAI,WAAU;;;;;;0BAEf,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6HAAA,CAAA,QAAK;wCAAC,WAAU;;0DACf,6LAAC,2MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAClB;;;;;;;kDAEH,6LAAC,6HAAA,CAAA,QAAK;wCACJ,WAAW,GAAG,qBAAqB,yBAAyB,CAAC;;0DAE7D,6LAAC,8NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB;;;;;;;;;;;;;0CAIL,6LAAC;gCAAG,WAAU;0CACX,QAAQ,KAAK;;;;;;0CAGhB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAU;oCACV,yBAAyB;wCAAE,QAAQ,QAAQ,eAAe,IAAI,QAAQ,WAAW;oCAAC;;;;;;;;;;;4BAIrF,wBAAwB,8BACvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAY;oCACnB;oCAAoB;;;;;;;;;;;;;kCAMvC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;kCAET,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,6LAAC,4HAAA,CAAA,OAAI;gCAAa,WAAU;0CAC1B,cAAA,6LAAC,4HAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;;;;;;sDAEvB,6LAAC;4CAAI,WAAU;sDAA2C,KAAK,KAAK;;;;;;sDACpE,6LAAC;4CAAI,WAAU;sDAA4C,KAAK,KAAK;;;;;;sDACrE,6LAAC;4CAAI,WAAU;sDAAiC,KAAK,WAAW;;;;;;;;;;;;+BAPzD;;;;;;;;;;kCAcf,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;;kDAEV,6LAAC,4HAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,6LAAC,6IAAA,CAAA,UAAmB;4CAAC,SAAS;;;;;;;;;;;kDAIhC,6LAAC,4HAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,6LAAC,4HAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAyB;;;;;;;8DAG/C,6LAAC;oDAAI,WAAU;8DACZ,gBAAgB,GAAG,CAAC,CAAC,SAAS,sBAC7B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4DAET,SAAS;gEAAE,SAAS;gEAAG,GAAG;4DAAG;4DAC7B,SAAS;gEAAE,SAAS;gEAAG,GAAG;4DAAE;4DAC5B,YAAY;gEAAE,UAAU;gEAAK,OAAO,MAAM,QAAQ;4DAAI;4DACtD,WAAU;;8EAEV,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,QAAQ,IAAI;wEAAC,WAAU;;;;;;;;;;;8EAE1B,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;sFAAoC,QAAQ,KAAK;;;;;;sFAC/D,6LAAC;4EAAE,WAAU;sFAAiD,QAAQ,WAAW;;;;;;;;;;;;;2DAX9E;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAqBjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;;kDAGV,6LAAC,4HAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,6LAAC,4HAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAyB;;;;;;;8DAG3C,6LAAC;oDAAI,WAAU;8DACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,6LAAC;4DAAgB,WAAU;sEACzB,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,KAAK,IAAI;4EAAC,WAAU;;;;;;;;;;;kFAEvB,6LAAC;;0FACC,6LAAC;gFAAE,WAAU;0FAAiC,KAAK,KAAK;;;;;;0FACxD,6LAAC;gFAAE,WAAU;0FAA+B,KAAK,KAAK;;;;;;;;;;;;;;;;;;2DAPlD;;;;;;;;;;;;;;;;;;;;;kDAiBlB,6LAAC,4HAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,6LAAC,4HAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC,uNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDAAyB;;;;;;;gDAKjD,QAAQ,QAAQ,EAAE,SAAS,QAAQ,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,mBAC1D,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;;8EACf,6LAAC,2MAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;gEAAY;;;;;;;sEAGjC,6LAAC;4DACC,WAAU;4DACV,OAAO,iBAAiB,MAAM;4DAC9B,UAAU;sEAET,QAAQ,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,qBAC3B,6LAAC;oEAA0B,OAAO,KAAK,IAAI,CAAC,EAAE;;wEAC3C,KAAK,IAAI,CAAC,KAAK;wEAAC;wEAAK,KAAK,IAAI,CAAC,OAAO,CAAC,MAAM;wEAAC;wEAAE,KAAK,IAAI,CAAC,OAAO,CAAC,YAAY;wEAC9E,CAAC,KAAK,IAAI,CAAC,gBAAgB,IAAI;;mEAFrB,KAAK,IAAI,CAAC,EAAE;;;;;;;;;;;;;;;;8DASjC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;;sFACf,6LAAC,2MAAA,CAAA,UAAO;4EAAC,WAAU;;;;;;wEAAY;wEAE9B,wBAAwB,sBACvB,6LAAC;4EAAK,WAAU;;gFAAgC;gFACvC;gFAAoB;;;;;;;;;;;;;8EAIjC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,8HAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,MAAK;4EACL,WAAU;4EACV,SAAS;4EACT,UAAU,YAAY;sFAEtB,cAAA,6LAAC,uNAAA,CAAA,cAAW;gFAAC,WAAU;;;;;;;;;;;sFAEzB,6LAAC;4EAAK,WAAU;sFAAuC;;;;;;sFACvD,6LAAC,8HAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,MAAK;4EACL,WAAU;4EACV,SAAS;4EACT,UAAU,wBAAwB,QAAQ,YAAY;sFAEtD,cAAA,6LAAC,mNAAA,CAAA,YAAS;gFAAC,WAAU;;;;;;;;;;;;;;;;;gEAGxB,wBAAwB,QAAQ,WAAW,qCAC1C,6LAAC;oEAAE,WAAU;8EAA4B;;;;;;;;;;;;sEAM7C,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;;sFACf,6LAAC,2MAAA,CAAA,UAAO;4EAAC,WAAU;;;;;;wEAAY;;;;;;;8EAGjC,6LAAC;oEACC,WAAU;oEACV,OAAO;oEACP,UAAU;8EAET,2BAA2B,GAAG,CAAC,CAAC,qBAC/B,6LAAC;4EAAkB,OAAO;sFACvB;2EADU;;;;;;;;;;;;;;;;;;;;;;8DAQrB,6LAAC,kJAAA,CAAA,UAAoB;oDACnB,WAAW,QAAQ,EAAE;oDACrB,WAAW,iBAAiB,MAAM,GAAG,QAAQ,EAAE,CAAC,QAAQ,CAAC;oDACzD,MAAM,QAAQ,KAAK;oDACnB,OAAO,WAAW,iBAAiB,SAAS,UAAU,QAAQ,UAAU,EAAE,iBAAiB,UAAU;oDACrG,UAAU;oDACV,OAAO,CAAC;wDACN,8BAA8B;wDAC9B,IAAI,QAAQ,MAAM,IAAI,MAAM,OAAO,CAAC,QAAQ,MAAM,KAAK,QAAQ,MAAM,CAAC,MAAM,GAAG,GAAG;4DAChF,MAAM,aAAa,QAAQ,MAAM,CAAC,EAAE;4DACpC,OAAO,OAAO,eAAe,WAAW,aAAa,WAAW,GAAG;wDACrE;wDACA,+BAA+B;wDAC/B,OAAO,QAAQ,MAAM,EAAE,OAAO,CAAC,EAAE,EAAE,MAAM,OAClC,QAAQ,KAAK,EAAE,OAAO,CAAC,EAAE,EAAE,MAAM,OAAO,OACxC,QAAQ,aAAa,EAAE,SAAS,OAAO,OAAO;oDACvD,CAAC;oDACD,UAAU;oDACV,aAAa;oDACb,UAAU,CAAC,wBAAyB,QAAQ,QAAQ,EAAE,SAAS,QAAQ,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC;oDACrG,WAAU;;;;;;8DAGZ,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,8HAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,WAAU;4DACV,SAAS;;gEAER,wBACC,6LAAC,yNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;yFAExB,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAElB,UAAU,UAAU;;;;;;;sEAEvB,6LAAC,8HAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,WAAU;4DACV,SAAS;;8EAET,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;;kDAQ3C,6LAAC;wCAAI,WAAU;kDACZ;4CACC;gDAAE,MAAM,yMAAA,CAAA,SAAM;gDAAE,MAAM;4CAAqB;4CAC3C;gDAAE,MAAM,uMAAA,CAAA,QAAK;gDAAE,MAAM;4CAAgB;4CACrC;gDAAE,MAAM,uMAAA,CAAA,QAAK;gDAAE,MAAM;4CAAiB;yCACvC,CAAC,GAAG,CAAC,CAAC,WAAW,sBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,UAAU;oDAAK,OAAO,MAAM,QAAQ;gDAAI;gDACtD,WAAU;;kEAEV,6LAAC,UAAU,IAAI;wDAAC,WAAU;;;;;;kEAC1B,6LAAC;wDAAK,WAAU;kEAAiC,UAAU,IAAI;;;;;;;+CAP1D;;;;;;;;;;;;;;;;;;;;;;kCAef,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;kCAExC,cAAA,6LAAC,4HAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,4HAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAG,WAAU;kDAA8C;;;;;;kDAC5D,6LAAC;wCAAE,WAAU;kDAA+C;;;;;;kDAG5D,6LAAC,8HAAA,CAAA,SAAM;wCAAC,WAAU;wCAAQ,SAAS;;0DACjC,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAgE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpG;GAlqBwB;;QAKoC,uIAAA,CAAA,qBAAkB;;;KALtD", "debugId": null}}, {"offset": {"line": 2208, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Benzochem%20Industries%20v2/edit-13%20%28%20clone%20%29/Benzochem-Industries/user/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,mKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG,mKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2274, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Benzochem%20Industries%20v2/edit-13%20%28%20clone%20%29/Benzochem-Industries/user/components/product-specifications.tsx"], "sourcesContent": ["\"use client\"\nimport { motion } from \"framer-motion\"\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from \"@/components/ui/tabs\"\nimport type { Product } from \"@/lib/types\"\n\ninterface ProductSpecificationsProps {\n  product: Product\n}\n\nexport default function ProductSpecifications({ product }: ProductSpecificationsProps) {\n\n  const getMetafieldValue = (key: string) => {\n    if (!product.metafields || !Array.isArray(product.metafields)) {\n      return ''\n    }\n    return product.metafields.find(field => field && field.key === key)?.value || ''\n  }\n\n  // Helper function to safely parse molecular formula\n  const getMolecularFormula = () => {\n    try {\n      const formula = getMetafieldValue(\"molecular_formula\");\n      if (!formula) return \"N/A\";\n      \n      // Try to parse as JSON (Shopify format)\n      const parsed = JSON.parse(formula);\n      if (parsed.children && parsed.children[0] && parsed.children[0].children && parsed.children[0].children[0]) {\n        return parsed.children[0].children[0].value;\n      }\n      return formula; // Return as-is if not in expected format\n    } catch (error) {\n      // If JSON parsing fails, return the raw value or a default\n      const formula = getMetafieldValue(\"molecular_formula\");\n      return formula || \"N/A\";\n    }\n  };\n\n  // Helper function to safely parse molecular weight\n  const getMolecularWeight = () => {\n    try {\n      const weight = getMetafieldValue(\"molecular_weight\");\n      if (!weight) return \"N/A\";\n      \n      // Try to parse as JSON (Shopify format)\n      const parsed = JSON.parse(weight);\n      if (parsed.value) {\n        return `${parsed.value} g/mol`;\n      }\n      return `${weight} g/mol`; // Return as-is if not in expected format\n    } catch (error) {\n      // If JSON parsing fails, return the raw value or a default\n      const weight = getMetafieldValue(\"molecular_weight\");\n      return weight ? `${weight} g/mol` : \"N/A\";\n    }\n  };\n\n  // Helper function to safely parse applications\n  const getApplications = () => {\n    try {\n      const apps = getMetafieldValue(\"recommended_applications\");\n      if (!apps) return [];\n      \n      // Try to parse as JSON (Shopify format)\n      const parsed = JSON.parse(apps);\n      if (parsed.children) {\n        return parsed.children.filter((app: { level: number }) => app.level === 3);\n      }\n      return []; // Return empty array if not in expected format\n    } catch (error) {\n      // If JSON parsing fails, return empty array\n      return [];\n    }\n  };\n\n  return (\n    <div>\n      <h2 className=\"text-2xl font-medium pt-10 mb-6 text-gray-900 dark:text-gray-100\">Product Specifications</h2>\n\n      <Tabs defaultValue=\"technical\" className=\"w-full\">\n        <TabsList className={`grid w-full ${getMetafieldValue(\"recommended_applications\") ? \"grid-cols-4\" : \"grid-cols-3\"} mb-8`}>\n          <TabsTrigger value=\"technical\">Technical Data</TabsTrigger>\n          {getMetafieldValue(\"recommended_applications\") && (\n            <TabsTrigger value=\"applications\">Applications</TabsTrigger>\n          )}\n          <TabsTrigger value=\"safety\">Safety & Handling</TabsTrigger>\n          <TabsTrigger value=\"shipping\">Shipping & Storage</TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"technical\" className=\"space-y-6\">\n          <motion.div initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.3 }}>\n            <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden\">\n              <table className=\"w-full\">\n                <tbody>\n                  <tr className=\"border-b border-gray-200 dark:border-gray-700\">\n                    <th className=\"text-left py-3 px-4 bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 font-medium w-1/3\">\n                      Chemical Name\n                    </th>\n                    <td className=\"py-3 px-4 text-gray-700 dark:text-gray-300\">{product.title || \"\"}</td>\n                  </tr>\n                  <tr className=\"border-b border-gray-200 dark:border-gray-700\">\n                    <th className=\"text-left py-3 px-4 bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 font-medium\">CAS Number</th>\n                    <td className=\"py-3 px-4 text-gray-700 dark:text-gray-300\">{getMetafieldValue(\"cas_number\") || \"N/A\"}</td>\n                  </tr>\n                  <tr className=\"border-b border-gray-200 dark:border-gray-700\">\n                    <th className=\"text-left py-3 px-4 bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 font-medium\">\n                      Molecular Formula\n                    </th>\n                    <td className=\"py-3 px-4 text-gray-700 dark:text-gray-300\">{getMolecularFormula()}</td>\n                  </tr>\n                  <tr className=\"border-b border-gray-200 dark:border-gray-700\">\n                    <th className=\"text-left py-3 px-4 bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 font-medium\">Molecular Weight</th>\n                    <td className=\"py-3 px-4 text-gray-700 dark:text-gray-300\">{getMolecularWeight()}</td>\n                  </tr>\n                  <tr className=\"border-b border-gray-200 dark:border-gray-700\">\n                    <th className=\"text-left py-3 px-4 bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 font-medium\">Appearance</th>\n                    <td className=\"py-3 px-4 text-gray-700 dark:text-gray-300\">{getMetafieldValue(\"appearance\") || \"N/A\"}</td>\n                  </tr>\n                  <tr className=\"border-b border-gray-200 dark:border-gray-700\">\n                    <th className=\"text-left py-3 px-4 bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 font-medium\">Purity</th>\n                    <td className=\"py-3 px-4 text-gray-700 dark:text-gray-300\">{getMetafieldValue(\"purity\") ? `${getMetafieldValue(\"purity\")}%` : \"N/A\"}</td>\n                  </tr>\n                  <tr className=\"border-b border-gray-200 dark:border-gray-700\">\n                    <th className=\"text-left py-3 px-4 bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 font-medium\">Solubility</th>\n                    <td className=\"py-3 px-4 text-gray-700 dark:text-gray-300\">{getMetafieldValue(\"solubility\") || \"N/A\"}</td>\n                  </tr>\n                  <tr>\n                    <th className=\"text-left py-3 px-4 bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 font-medium\">pH Value</th>\n                    <td className=\"py-3 px-4 text-gray-700 dark:text-gray-300\">{getMetafieldValue(\"ph_value\") || \"N/A\"}</td>\n                  </tr>\n                </tbody>\n              </table>\n            </div>\n          </motion.div>\n        </TabsContent>\n\n        <TabsContent value=\"applications\" className=\"space-y-6\">\n          <motion.div initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.3 }}>\n            <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6\">\n              <h3 className=\"text-lg font-medium mb-4 text-gray-900 dark:text-gray-100\">Recommended Applications</h3>\n              <ul className=\"space-y-4\">\n                {getApplications().length > 0 ? (\n                  getApplications().map((application: any, index: any) => (\n                    <li key={index} className=\"flex items-start\">\n                      <span className=\"h-5 w-5 rounded-full bg-teal-100 dark:bg-teal-900 text-teal-600 dark:text-teal-300 flex items-center justify-center text-xs font-medium mr-3 mt-0.5\">\n                        {index + 1}\n                      </span>\n                      <div>\n                        <p className=\"font-medium text-gray-900 dark:text-gray-100\">{application.children?.[0]?.value || `Application ${index + 1}`}</p>\n                        <p className=\"text-gray-600 dark:text-gray-400 text-sm mt-1\">Provides excellent results with standard processing parameters.</p>\n                      </div>\n                    </li>\n                  ))\n                ) : (\n                  <li className=\"flex items-start\">\n                    <span className=\"h-5 w-5 rounded-full bg-teal-100 dark:bg-teal-900 text-teal-600 dark:text-teal-300 flex items-center justify-center text-xs font-medium mr-3 mt-0.5\">\n                      1\n                    </span>\n                    <div>\n                      <p className=\"font-medium text-gray-900 dark:text-gray-100\">Industrial Manufacturing</p>\n                      <p className=\"text-gray-600 dark:text-gray-400 text-sm mt-1\">Suitable for various industrial applications and manufacturing processes.</p>\n                    </div>\n                  </li>\n                )}\n              </ul>\n            </div>\n          </motion.div>\n        </TabsContent>\n\n        <TabsContent value=\"safety\" className=\"space-y-6\">\n          <motion.div initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.3 }}>\n            <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6\">\n              <h3 className=\"text-lg font-medium mb-4 text-gray-900 dark:text-gray-100\">Safety Information</h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <h4 className=\"font-medium mb-2 text-gray-900 dark:text-gray-100\">Handling Precautions</h4>\n                  <ul className=\"space-y-2 text-gray-600 dark:text-gray-400\">\n                    <li>• Wear appropriate protective equipment</li>\n                    <li>• Use in well-ventilated areas</li>\n                    <li>• Avoid contact with skin and eyes</li>\n                    <li>• Wash hands thoroughly after handling</li>\n                  </ul>\n                </div>\n                <div>\n                  <h4 className=\"font-medium mb-2 text-gray-900 dark:text-gray-100\">Storage Recommendations</h4>\n                  <ul className=\"space-y-2 text-gray-600 dark:text-gray-400\">\n                    <li>• Store in a cool, dry place</li>\n                    <li>• Keep container tightly closed</li>\n                    <li>• Protect from direct sunlight</li>\n                    <li>• Keep away from incompatible materials</li>\n                  </ul>\n                </div>\n              </div>\n\n              <div className=\"mt-6\">\n                <h4 className=\"font-medium mb-2 text-gray-900 dark:text-gray-100\">First Aid Measures</h4>\n                <div className=\"bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-md p-4\">\n                  <p className=\"mb-2 text-gray-600 dark:text-gray-400\">\n                    <strong className=\"text-gray-900 dark:text-gray-100\">Eye Contact:</strong> Rinse cautiously with water for several minutes. Remove contact lenses\n                    if present and easy to do. Continue rinsing.\n                  </p>\n                  <p className=\"mb-2 text-gray-600 dark:text-gray-400\">\n                    <strong className=\"text-gray-900 dark:text-gray-100\">Skin Contact:</strong> Wash with plenty of soap and water. If skin irritation occurs, get\n                    medical advice/attention.\n                  </p>\n                  <p className=\"mb-2 text-gray-600 dark:text-gray-400\">\n                    <strong className=\"text-gray-900 dark:text-gray-100\">Inhalation:</strong> Remove person to fresh air and keep comfortable for breathing.\n                  </p>\n                  <p className=\"text-gray-600 dark:text-gray-400\">\n                    <strong className=\"text-gray-900 dark:text-gray-100\">Ingestion:</strong> Rinse mouth. Do NOT induce vomiting. Seek immediate medical attention.\n                  </p>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </TabsContent>\n\n        <TabsContent value=\"shipping\" className=\"space-y-6\">\n          <motion.div initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.3 }}>\n            <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6\">\n              <h3 className=\"text-lg font-medium mb-4 text-gray-900 dark:text-gray-100\">Shipping & Storage Information</h3>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n                <div className=\"border border-gray-200 dark:border-gray-600 rounded-md p-4 bg-gray-50 dark:bg-gray-900\">\n                  <h4 className=\"font-medium mb-2 text-gray-900 dark:text-gray-100\">Packaging Options</h4>\n                  <ul className=\"space-y-2 text-gray-700 dark:text-gray-300\">\n                    <li>• Standard packaging</li>\n                    <li>• Bulk packaging (100kg+)</li>\n                    <li>• Industrial packaging (1000kg+)</li>\n                    <li>• Custom packaging available upon request</li>\n                  </ul>\n                </div>\n                <div className=\"border border-gray-200 dark:border-gray-600 rounded-md p-4 bg-gray-50 dark:bg-gray-900\">\n                  <h4 className=\"font-medium mb-2 text-gray-900 dark:text-gray-100\">Shipping Classification</h4>\n                  <ul className=\"space-y-2 text-gray-700 dark:text-gray-300\">\n                    <li>• Hazard Class: Non-hazardous</li>\n                    <li>• UN Number: Not applicable</li>\n                    <li>• Proper Shipping Name: Chemical product</li>\n                  </ul>\n                </div>\n              </div>\n\n              <div>\n                <h4 className=\"font-medium mb-2 text-gray-900 dark:text-gray-100\">Storage Conditions</h4>\n                <div className=\"bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-md p-4\">\n                  <p className=\"mb-2 text-gray-700 dark:text-gray-300\">\n                    <strong className=\"text-gray-900 dark:text-gray-100\">Temperature:</strong> Store at room temperature (15-25°C)\n                  </p>\n                  <p className=\"mb-2 text-gray-700 dark:text-gray-300\">\n                    <strong className=\"text-gray-900 dark:text-gray-100\">Humidity:</strong> Keep in a dry environment (&lt;60% relative humidity)\n                  </p>\n                  <p className=\"text-gray-700 dark:text-gray-300\">\n                    <strong className=\"text-gray-900 dark:text-gray-100\">Light Sensitivity:</strong> Protect from direct sunlight and UV light\n                  </p>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </TabsContent>\n      </Tabs>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AAFA;;;;AASe,SAAS,sBAAsB,EAAE,OAAO,EAA8B;IAEnF,MAAM,oBAAoB,CAAC;QACzB,IAAI,CAAC,QAAQ,UAAU,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ,UAAU,GAAG;YAC7D,OAAO;QACT;QACA,OAAO,QAAQ,UAAU,CAAC,IAAI,CAAC,CAAA,QAAS,SAAS,MAAM,GAAG,KAAK,MAAM,SAAS;IAChF;IAEA,oDAAoD;IACpD,MAAM,sBAAsB;QAC1B,IAAI;YACF,MAAM,UAAU,kBAAkB;YAClC,IAAI,CAAC,SAAS,OAAO;YAErB,wCAAwC;YACxC,MAAM,SAAS,KAAK,KAAK,CAAC;YAC1B,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,CAAC,EAAE,IAAI,OAAO,QAAQ,CAAC,EAAE,CAAC,QAAQ,IAAI,OAAO,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE,EAAE;gBAC1G,OAAO,OAAO,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,KAAK;YAC7C;YACA,OAAO,SAAS,yCAAyC;QAC3D,EAAE,OAAO,OAAO;YACd,2DAA2D;YAC3D,MAAM,UAAU,kBAAkB;YAClC,OAAO,WAAW;QACpB;IACF;IAEA,mDAAmD;IACnD,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,SAAS,kBAAkB;YACjC,IAAI,CAAC,QAAQ,OAAO;YAEpB,wCAAwC;YACxC,MAAM,SAAS,KAAK,KAAK,CAAC;YAC1B,IAAI,OAAO,KAAK,EAAE;gBAChB,OAAO,GAAG,OAAO,KAAK,CAAC,MAAM,CAAC;YAChC;YACA,OAAO,GAAG,OAAO,MAAM,CAAC,EAAE,yCAAyC;QACrE,EAAE,OAAO,OAAO;YACd,2DAA2D;YAC3D,MAAM,SAAS,kBAAkB;YACjC,OAAO,SAAS,GAAG,OAAO,MAAM,CAAC,GAAG;QACtC;IACF;IAEA,+CAA+C;IAC/C,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,OAAO,kBAAkB;YAC/B,IAAI,CAAC,MAAM,OAAO,EAAE;YAEpB,wCAAwC;YACxC,MAAM,SAAS,KAAK,KAAK,CAAC;YAC1B,IAAI,OAAO,QAAQ,EAAE;gBACnB,OAAO,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,MAA2B,IAAI,KAAK,KAAK;YAC1E;YACA,OAAO,EAAE,EAAE,+CAA+C;QAC5D,EAAE,OAAO,OAAO;YACd,4CAA4C;YAC5C,OAAO,EAAE;QACX;IACF;IAEA,qBACE,6LAAC;;0BACC,6LAAC;gBAAG,WAAU;0BAAmE;;;;;;0BAEjF,6LAAC,4HAAA,CAAA,OAAI;gBAAC,cAAa;gBAAY,WAAU;;kCACvC,6LAAC,4HAAA,CAAA,WAAQ;wBAAC,WAAW,CAAC,YAAY,EAAE,kBAAkB,8BAA8B,gBAAgB,cAAc,KAAK,CAAC;;0CACtH,6LAAC,4HAAA,CAAA,cAAW;gCAAC,OAAM;0CAAY;;;;;;4BAC9B,kBAAkB,6CACjB,6LAAC,4HAAA,CAAA,cAAW;gCAAC,OAAM;0CAAe;;;;;;0CAEpC,6LAAC,4HAAA,CAAA,cAAW;gCAAC,OAAM;0CAAS;;;;;;0CAC5B,6LAAC,4HAAA,CAAA,cAAW;gCAAC,OAAM;0CAAW;;;;;;;;;;;;kCAGhC,6LAAC,4HAAA,CAAA,cAAW;wBAAC,OAAM;wBAAY,WAAU;kCACvC,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAAG,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAAG,YAAY;gCAAE,UAAU;4BAAI;sCACrG,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAM,WAAU;8CACf,cAAA,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAG,WAAU;kEAAqG;;;;;;kEAGnH,6LAAC;wDAAG,WAAU;kEAA8C,QAAQ,KAAK,IAAI;;;;;;;;;;;;0DAE/E,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAG,WAAU;kEAA+F;;;;;;kEAC7G,6LAAC;wDAAG,WAAU;kEAA8C,kBAAkB,iBAAiB;;;;;;;;;;;;0DAEjG,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAG,WAAU;kEAA+F;;;;;;kEAG7G,6LAAC;wDAAG,WAAU;kEAA8C;;;;;;;;;;;;0DAE9D,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAG,WAAU;kEAA+F;;;;;;kEAC7G,6LAAC;wDAAG,WAAU;kEAA8C;;;;;;;;;;;;0DAE9D,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAG,WAAU;kEAA+F;;;;;;kEAC7G,6LAAC;wDAAG,WAAU;kEAA8C,kBAAkB,iBAAiB;;;;;;;;;;;;0DAEjG,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAG,WAAU;kEAA+F;;;;;;kEAC7G,6LAAC;wDAAG,WAAU;kEAA8C,kBAAkB,YAAY,GAAG,kBAAkB,UAAU,CAAC,CAAC,GAAG;;;;;;;;;;;;0DAEhI,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAG,WAAU;kEAA+F;;;;;;kEAC7G,6LAAC;wDAAG,WAAU;kEAA8C,kBAAkB,iBAAiB;;;;;;;;;;;;0DAEjG,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA+F;;;;;;kEAC7G,6LAAC;wDAAG,WAAU;kEAA8C,kBAAkB,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQzG,6LAAC,4HAAA,CAAA,cAAW;wBAAC,OAAM;wBAAe,WAAU;kCAC1C,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAAG,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAAG,YAAY;gCAAE,UAAU;4BAAI;sCACrG,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAC1E,6LAAC;wCAAG,WAAU;kDACX,kBAAkB,MAAM,GAAG,IAC1B,kBAAkB,GAAG,CAAC,CAAC,aAAkB,sBACvC,6LAAC;gDAAe,WAAU;;kEACxB,6LAAC;wDAAK,WAAU;kEACb,QAAQ;;;;;;kEAEX,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAgD,YAAY,QAAQ,EAAE,CAAC,EAAE,EAAE,SAAS,CAAC,YAAY,EAAE,QAAQ,GAAG;;;;;;0EAC3H,6LAAC;gEAAE,WAAU;0EAAgD;;;;;;;;;;;;;+CANxD;;;;sEAWX,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAsJ;;;;;;8DAGtK,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAA+C;;;;;;sEAC5D,6LAAC;4DAAE,WAAU;sEAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS3E,6LAAC,4HAAA,CAAA,cAAW;wBAAC,OAAM;wBAAS,WAAU;kCACpC,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAAG,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAAG,YAAY;gCAAE,UAAU;4BAAI;sCACrG,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAC1E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAoD;;;;;;kEAClE,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;;;;;;;;;;;;;0DAGR,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAoD;;;;;;kEAClE,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;;;;;;;;;;;;;;;;;;;kDAKV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAoD;;;;;;0DAClE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;;0EACX,6LAAC;gEAAO,WAAU;0EAAmC;;;;;;4DAAqB;;;;;;;kEAG5E,6LAAC;wDAAE,WAAU;;0EACX,6LAAC;gEAAO,WAAU;0EAAmC;;;;;;4DAAsB;;;;;;;kEAG7E,6LAAC;wDAAE,WAAU;;0EACX,6LAAC;gEAAO,WAAU;0EAAmC;;;;;;4DAAoB;;;;;;;kEAE3E,6LAAC;wDAAE,WAAU;;0EACX,6LAAC;gEAAO,WAAU;0EAAmC;;;;;;4DAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQpF,6LAAC,4HAAA,CAAA,cAAW;wBAAC,OAAM;wBAAW,WAAU;kCACtC,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAAG,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAAG,YAAY;gCAAE,UAAU;4BAAI;sCACrG,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAE1E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAoD;;;;;;kEAClE,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;;;;;;;;;;;;;0DAGR,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAoD;;;;;;kEAClE,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;;;;;;;;;;;;;;;;;;;kDAKV,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAoD;;;;;;0DAClE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;;0EACX,6LAAC;gEAAO,WAAU;0EAAmC;;;;;;4DAAqB;;;;;;;kEAE5E,6LAAC;wDAAE,WAAU;;0EACX,6LAAC;gEAAO,WAAU;0EAAmC;;;;;;4DAAkB;;;;;;;kEAEzE,6LAAC;wDAAE,WAAU;;0EACX,6LAAC;gEAAO,WAAU;0EAAmC;;;;;;4DAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpG;KA5PwB", "debugId": null}}, {"offset": {"line": 3283, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Benzochem%20Industries%20v2/edit-13%20%28%20clone%20%29/Benzochem-Industries/user/components/product-card.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport Image from \"next/image\"\nimport Link from \"next/link\"\nimport { motion } from \"framer-motion\"\nimport { ShoppingBag, Info } from \"lucide-react\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { <PERSON>, CardContent, CardFooter } from \"@/components/ui/card\"\nimport { getProductImageUrl, getProductCategory } from \"@/lib/image-utils\"\nimport type { Product } from \"@/lib/types\"\n\nexport default function ProductCard({ product }: { product: Product }) {\n  const [isHovered, setIsHovered] = useState(false)\n  const [imageError, setImageError] = useState(false)\n\n  // Helper function to get the main image URL\n  const getImageUrl = () => {\n    if (imageError) return \"/placeholder-chemical.jpg\"\n    return getProductImageUrl(product)\n  }\n\n  // Helper function to clean and format description\n  const getCleanDescription = () => {\n    if (!product.description) return 'High-quality chemical product for industrial applications.'\n    \n    // Remove HTML tags and decode HTML entities\n    const cleanText = product.description\n      .replace(/<[^>]*>/g, '') // Remove HTML tags\n      .replace(/&nbsp;/g, ' ') // Replace non-breaking spaces\n      .replace(/&amp;/g, '&') // Replace HTML entities\n      .replace(/&lt;/g, '<')\n      .replace(/&gt;/g, '>')\n      .replace(/&quot;/g, '\"')\n      .replace(/&#39;/g, \"'\")\n      .replace(/\\s+/g, ' ') // Replace multiple spaces with single space\n      .trim() // Remove leading/trailing whitespace\n    \n    return cleanText || 'High-quality chemical product for industrial applications.'\n  }\n\n  // Helper function to get metafield value - supports both 'custom' and 'chemical' namespaces\n  const getMetafieldValue = (key: string, namespace: string = 'custom') => {\n    if (!product.metafields || !Array.isArray(product.metafields)) {\n      return ''\n    }\n    // Try the specified namespace first, then try 'chemical' namespace as fallback\n    let metafield = product.metafields.find(m => m?.namespace === namespace && m?.key === key)\n    if (!metafield && namespace !== 'chemical') {\n      metafield = product.metafields.find(m => m?.namespace === 'chemical' && m?.key === key)\n    }\n    return metafield?.value || ''\n  }\n\n  const purity = getMetafieldValue('purity') || getMetafieldValue('purity', 'chemical')\n  const packaging = getMetafieldValue('package_size') || getMetafieldValue('packaging', 'chemical')\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      whileInView={{ opacity: 1, y: 0 }}\n      viewport={{ once: true }}\n      transition={{ duration: 0.5 }}\n    >\n      <Card\n        className=\"overflow-hidden border-0 bg-card shadow-sm transition-all duration-300 hover:shadow-md\"\n        onMouseEnter={() => setIsHovered(true)}\n        onMouseLeave={() => setIsHovered(false)}\n      >\n        <div className=\"relative aspect-square overflow-hidden\">\n          <Image\n            src={getImageUrl()}\n            alt={product.title}\n            fill\n            className=\"object-cover transition-transform duration-500 hover:scale-105\"\n            onError={() => setImageError(true)}\n          />\n\n          {isHovered && (\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              className=\"absolute inset-0 flex items-center justify-center bg-black/40\"\n            >\n              <Link href={`/products/${product.id.split('/').pop()}`}>\n                <Button variant=\"secondary\" size=\"sm\" className=\"mr-2\">\n                  <Info className=\"h-4 w-4 mr-1\" />\n                  Details\n                </Button>\n              </Link>\n            </motion.div>\n          )}\n\n          <div className=\"absolute top-2 right-2 bg-primary text-primary-foreground text-xs font-medium px-2 py-1 rounded\">\n            {getProductCategory(product)}\n          </div>\n        </div>\n\n        <CardContent className=\"p-4\">\n          <Link href={`/products/${product.id.split('/').pop()}`}>\n            <h3 className=\"font-medium text-lg mb-1 text-card-foreground hover:text-primary transition-colors\">\n              {product.title}\n            </h3>\n          </Link>\n          <p className=\"text-sm text-muted-foreground mb-2\">\n            {getCleanDescription().slice(0, 100)}...\n          </p>\n          <div className=\"flex flex-wrap gap-2 text-xs\">\n            {purity && (\n              <span className=\"bg-muted text-muted-foreground px-2 py-1 rounded\">\n                Purity: {purity}\n              </span>\n            )}\n            {packaging && (\n              <span className=\"bg-muted text-muted-foreground px-2 py-1 rounded\">\n                {packaging}\n              </span>\n            )}\n          </div>\n        </CardContent>\n\n        <CardFooter className=\"p-4 pt-0 flex items-center justify-end\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"text-primary hover:text-primary hover:bg-primary/10\"\n          >\n            <ShoppingBag className=\"h-4 w-4 mr-1\" />\n            Request Quote\n          </Button>\n        </CardFooter>\n      </Card>\n    </motion.div>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAYe,SAAS,YAAY,EAAE,OAAO,EAAwB;;IACnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,4CAA4C;IAC5C,MAAM,cAAc;QAClB,IAAI,YAAY,OAAO;QACvB,OAAO,CAAA,GAAA,wHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,kDAAkD;IAClD,MAAM,sBAAsB;QAC1B,IAAI,CAAC,QAAQ,WAAW,EAAE,OAAO;QAEjC,4CAA4C;QAC5C,MAAM,YAAY,QAAQ,WAAW,CAClC,OAAO,CAAC,YAAY,IAAI,mBAAmB;SAC3C,OAAO,CAAC,WAAW,KAAK,8BAA8B;SACtD,OAAO,CAAC,UAAU,KAAK,wBAAwB;SAC/C,OAAO,CAAC,SAAS,KACjB,OAAO,CAAC,SAAS,KACjB,OAAO,CAAC,WAAW,KACnB,OAAO,CAAC,UAAU,KAClB,OAAO,CAAC,QAAQ,KAAK,4CAA4C;SACjE,IAAI,GAAG,qCAAqC;;QAE/C,OAAO,aAAa;IACtB;IAEA,4FAA4F;IAC5F,MAAM,oBAAoB,CAAC,KAAa,YAAoB,QAAQ;QAClE,IAAI,CAAC,QAAQ,UAAU,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ,UAAU,GAAG;YAC7D,OAAO;QACT;QACA,+EAA+E;QAC/E,IAAI,YAAY,QAAQ,UAAU,CAAC,IAAI,CAAC,CAAA,IAAK,GAAG,cAAc,aAAa,GAAG,QAAQ;QACtF,IAAI,CAAC,aAAa,cAAc,YAAY;YAC1C,YAAY,QAAQ,UAAU,CAAC,IAAI,CAAC,CAAA,IAAK,GAAG,cAAc,cAAc,GAAG,QAAQ;QACrF;QACA,OAAO,WAAW,SAAS;IAC7B;IAEA,MAAM,SAAS,kBAAkB,aAAa,kBAAkB,UAAU;IAC1E,MAAM,YAAY,kBAAkB,mBAAmB,kBAAkB,aAAa;IAEtF,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,UAAU;YAAE,MAAM;QAAK;QACvB,YAAY;YAAE,UAAU;QAAI;kBAE5B,cAAA,6LAAC,4HAAA,CAAA,OAAI;YACH,WAAU;YACV,cAAc,IAAM,aAAa;YACjC,cAAc,IAAM,aAAa;;8BAEjC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAK;4BACL,KAAK,QAAQ,KAAK;4BAClB,IAAI;4BACJ,WAAU;4BACV,SAAS,IAAM,cAAc;;;;;;wBAG9B,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,WAAU;sCAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI;0CACpD,cAAA,6LAAC,8HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAY,MAAK;oCAAK,WAAU;;sDAC9C,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;sCAOzC,6LAAC;4BAAI,WAAU;sCACZ,CAAA,GAAA,wHAAA,CAAA,qBAAkB,AAAD,EAAE;;;;;;;;;;;;8BAIxB,6LAAC,4HAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI;sCACpD,cAAA,6LAAC;gCAAG,WAAU;0CACX,QAAQ,KAAK;;;;;;;;;;;sCAGlB,6LAAC;4BAAE,WAAU;;gCACV,sBAAsB,KAAK,CAAC,GAAG;gCAAK;;;;;;;sCAEvC,6LAAC;4BAAI,WAAU;;gCACZ,wBACC,6LAAC;oCAAK,WAAU;;wCAAmD;wCACxD;;;;;;;gCAGZ,2BACC,6LAAC;oCAAK,WAAU;8CACb;;;;;;;;;;;;;;;;;;8BAMT,6LAAC,4HAAA,CAAA,aAAU;oBAAC,WAAU;8BACpB,cAAA,6LAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;;0CAEV,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;;;;;;AAOpD;GA1HwB;KAAA", "debugId": null}}, {"offset": {"line": 3543, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Benzochem%20Industries%20v2/edit-13%20%28%20clone%20%29/Benzochem-Industries/user/components/related-products.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { motion } from \"framer-motion\"\nimport ProductCard from \"@/components/product-card\"\nimport type { Product } from \"@/lib/types\"\nimport { apiClient, transformApiProductToProduct, type ApiProduct } from \"@/lib/api-client\"\n\ninterface RelatedProductsProps {\n  currentProductId: string\n  category?: string\n}\n\nexport default function RelatedProducts({ currentProductId, category }: RelatedProductsProps) {\n  const [products, setProducts] = useState<Product[]>([])\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    async function fetchRelatedProducts() {\n      try {\n        setLoading(true)\n        // Fetch products, optionally filtered by category\n        const response = await apiClient.getProducts({ \n          limit: 20,\n          collection: category \n        })\n        \n        if (response.success && response.data && Array.isArray(response.data)) {\n          // Transform API products to Product type and filter out current product\n          const allProducts = (response.data as ApiProduct[])\n            .map(transformApiProductToProduct)\n            .filter(product => product.id !== currentProductId)\n            .slice(0, 4) // Limit to 4 related products\n          \n          setProducts(allProducts)\n        } else {\n          setProducts([])\n        }\n      } catch (error) {\n        console.error(\"Error fetching related products:\", error)\n        setProducts([])\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    fetchRelatedProducts()\n  }, [currentProductId, category])\n\n  if (loading) {\n    return (\n      <div className=\"mt-24 mb-16\">\n        <h2 className=\"text-2xl font-bold mb-8\">Related Products</h2>\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\">\n          {[...Array(4)].map((_, i) => (\n            <div key={i} className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4 animate-pulse\">\n              <div className=\"h-40 w-full bg-gray-200 dark:bg-gray-700 rounded-md mb-4\" />\n              <div className=\"h-6 w-3/4 bg-gray-200 dark:bg-gray-700 rounded mb-2\" />\n              <div className=\"h-4 w-1/2 bg-gray-200 dark:bg-gray-700 rounded\" />\n            </div>\n          ))}\n        </div>\n      </div>\n    )\n  }\n\n  if (!products || products.length === 0) {\n    return null\n  }\n\n  return (\n    <div className=\"mt-24 mb-16\">\n      <h2 className=\"text-2xl font-bold mb-8\">Related Products</h2>\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ staggerChildren: 0.1 }}\n        className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\"\n      >\n        {products.map((product) => (\n          <ProductCard key={product.id} product={product} />\n        ))}\n      </motion.div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;;;AANA;;;;;AAae,SAAS,gBAAgB,EAAE,gBAAgB,EAAE,QAAQ,EAAwB;;IAC1F,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,eAAe;gBACb,IAAI;oBACF,WAAW;oBACX,kDAAkD;oBAClD,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,WAAW,CAAC;wBAC3C,OAAO;wBACP,YAAY;oBACd;oBAEA,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;wBACrE,wEAAwE;wBACxE,MAAM,cAAc,AAAC,SAAS,IAAI,CAC/B,GAAG,CAAC,uHAAA,CAAA,+BAA4B,EAChC,MAAM;0FAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;yFACjC,KAAK,CAAC,GAAG,GAAG,8BAA8B;;wBAE7C,YAAY;oBACd,OAAO;wBACL,YAAY,EAAE;oBAChB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,oCAAoC;oBAClD,YAAY,EAAE;gBAChB,SAAU;oBACR,WAAW;gBACb;YACF;YAEA;QACF;oCAAG;QAAC;QAAkB;KAAS;IAE/B,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA0B;;;;;;8BACxC,6LAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;4BAAY,WAAU;;8CACrB,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;2BAHP;;;;;;;;;;;;;;;;IASpB;IAEA,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG;QACtC,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA0B;;;;;;0BACxC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,iBAAiB;gBAAI;gBACnC,WAAU;0BAET,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC,iIAAA,CAAA,UAAW;wBAAkB,SAAS;uBAArB,QAAQ,EAAE;;;;;;;;;;;;;;;;AAKtC;GAxEwB;KAAA", "debugId": null}}]}