import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";

// Skeleton for an individual product card
function ProductCardSkeleton() {
  return (
    <Card className="overflow-hidden border-0 bg-white shadow-sm">
      <div className="relative aspect-square overflow-hidden">
        <Skeleton className="h-full w-full" /> {/* Image */}
        <Skeleton className="absolute top-2 right-2 h-5 w-20 rounded" /> {/* Category Tag */}
      </div>
      <CardContent className="p-4">
        <Skeleton className="h-6 w-3/4 mb-1 rounded" /> {/* Title */}
        <Skeleton className="h-4 w-full mb-1 rounded" /> {/* Description line 1 */}
        <Skeleton className="h-4 w-5/6 mb-2 rounded" /> {/* Description line 2 */}
        <div className="flex flex-wrap gap-2 text-xs mb-2">
          <Skeleton className="h-5 w-24 rounded" /> {/* Metafield 1 */}
          <Skeleton className="h-5 w-20 rounded" /> {/* Metafield 2 */}
        </div>
      </CardContent>
      <CardFooter className="p-4 pt-0 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Skeleton className="h-5 w-12 rounded" /> {/* Original Price */}
          <Skeleton className="h-5 w-12 rounded" /> {/* Discounted Price */}
        </div>
        <Skeleton className="h-9 w-28 rounded" /> {/* Add to Cart Button */}
      </CardFooter>
    </Card>
  );
}

// Skeleton for the featured products section
export default function FeaturedProductsSkeleton() {
  return (
    <div className="space-y-8">
      {/* Skeleton for Filter Buttons */}
      <div className="flex justify-center space-x-4 mb-8 flex-wrap gap-2">
        <Skeleton className="h-9 w-24 rounded-full" />
        <Skeleton className="h-9 w-28 rounded-full" />
        <Skeleton className="h-9 w-20 rounded-full" />
        <Skeleton className="h-9 w-32 rounded-full" />
      </div>

      {/* Skeleton for Product Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(8)].map((_, i) => ( // Show 8 skeleton cards
          <ProductCardSkeleton key={i} />
        ))}
      </div>
    </div>
  );
}