import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { randomBytes } from 'crypto'

interface CookiePreferences {
  essential: boolean
  analytics: boolean
  marketing: boolean
  functional: boolean
}

interface ConsentData {
  preferences: CookiePreferences
  timestamp: string
  userAgent?: string
  ipAddress?: string
  anonymousId?: string
}

const CONSENT_COOKIE_NAME = 'cookie_consent'
const ANONYMOUS_ID_COOKIE_NAME = 'anonymous_id'
const ADMIN_API_URL = process.env.NEXT_PUBLIC_ADMIN_API_URL || 'http://localhost:3001'

// Generate a secure anonymous ID
function generateAnonymousId(): string {
  return randomBytes(16).toString('hex')
}

// Helper function to get or create anonymous ID
async function getOrCreateAnonymousId(): Promise<string> {
  try {
    const cookieStore = await cookies()
    const existingId = cookieStore.get(ANONYMOUS_ID_COOKIE_NAME)
    
    if (existingId && existingId.value) {
      return existingId.value
    }
    
    // Generate new anonymous ID
    return generateAnonymousId()
  } catch (error) {
    console.error('Error getting anonymous ID:', error)
    return generateAnonymousId()
  }
}

// Helper function to get user info from cookie (optional)
async function getUserInfoFromCookie(): Promise<{ firstName: string; lastName: string; email: string } | null> {
  try {
    const cookieStore = await cookies()
    const userCookie = cookieStore.get('user_session')
    
    if (!userCookie) {
      return null
    }

    const userData = JSON.parse(decodeURIComponent(userCookie.value))
    console.log('🍪 Raw user session data:', userData)
    
    // Extract user info with fallbacks for missing fields
    const firstName = userData.firstName || userData.first_name || userData.name?.split(' ')[0] || 'User'
    const lastName = userData.lastName || userData.last_name || userData.name?.split(' ')[1] || 'Account'
    const email = userData.email || userData.userEmail || userData.emailAddress
    
    if (!email) {
      console.error('❌ No email found in user session data')
      return null
    }
    
    console.log('🍪 Extracted user info:', { firstName, lastName, email })
    
    return {
      firstName,
      lastName,
      email
    }
  } catch (error) {
    console.error('Error parsing user cookie:', error)
    return null
  }
}

export async function GET(request: NextRequest) {
  try {
    console.log('🍪 === COOKIE CONSENT GET REQUEST START ===')
    
    const userInfo = await getUserInfoFromCookie()
    const anonymousId = await getOrCreateAnonymousId()
    
    console.log('🍪 User info:', userInfo ? { email: userInfo.email } : 'Anonymous')
    console.log('🍪 Anonymous ID:', anonymousId)
    
    // Get local cookie for comparison
    const cookieStore = await cookies()
    const consentCookie = cookieStore.get(CONSENT_COOKIE_NAME)
    let localConsentData: ConsentData | null = null
    
    if (consentCookie) {
      try {
        let decodedValue = consentCookie.value
        
        // Check if the value is URL-encoded
        if (consentCookie.value.includes('%')) {
          decodedValue = decodeURIComponent(consentCookie.value)
        }
        
        // Validate that it's valid JSON before parsing
        if (decodedValue.trim().startsWith('{') && decodedValue.trim().endsWith('}')) {
          localConsentData = JSON.parse(decodedValue)
          console.log('�� Local cookie consent found:', localConsentData?.preferences)
        }
      } catch (parseError) {
        console.warn('🍪 Failed to parse consent cookie:', parseError)
      }
    } else {
      console.log('🍪 No local consent cookie found')
    }

    // ALWAYS check database for authoritative consent status
    let dbConsentValid = false
    let dbConsentData: any = null
    
    if (userInfo) {
      // Check database for logged-in user
      try {
        console.log('🍪 Checking database for logged-in user:', userInfo.email)
        const response = await fetch(`${ADMIN_API_URL}/api/public/cookie-consent?email=${encodeURIComponent(userInfo.email)}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        })

        if (response.ok) {
          const result = await response.json()
          console.log('🍪 Database response:', result)
          
          if (result.success && result.consent) {
            dbConsentValid = true
            dbConsentData = result.consent
            console.log('✅ Valid consent found in database for logged-in user')
          } else {
            console.log('❌ No valid consent found in database for logged-in user')
          }
        } else {
          console.log('❌ Database check failed with status:', response.status)
        }
      } catch (dbError) {
        console.error('❌ Database check failed:', dbError)
      }
    } else {
      // Check database for anonymous user
      try {
        console.log('🍪 Checking database for anonymous user:', anonymousId)
        const response = await fetch(`${ADMIN_API_URL}/api/public/cookie-consent?anonymousId=${encodeURIComponent(anonymousId)}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        })

        if (response.ok) {
          const result = await response.json()
          console.log('🍪 Database response:', result)
          
          if (result.success && result.consent) {
            dbConsentValid = true
            dbConsentData = result.consent
            console.log('✅ Valid consent found in database for anonymous user')
          } else {
            console.log('❌ No valid consent found in database for anonymous user')
          }
        } else {
          console.log('❌ Database check failed with status:', response.status)
        }
      } catch (dbError) {
        console.error('❌ Database check failed:', dbError)
      }
    }

    // If database has valid consent, return it
    if (dbConsentValid && dbConsentData) {
      console.log('✅ Returning valid consent from database')
      return NextResponse.json({
        hasConsent: true,
        preferences: dbConsentData.preferences,
        timestamp: dbConsentData.timestamp,
        isAnonymous: !userInfo,
        source: 'database'
      })
    }

    // If database doesn't have consent but local cookie exists, clear the local cookie
    if (!dbConsentValid && localConsentData) {
      console.log('🍪 Database consent missing but local cookie exists - clearing local cookie')
      const response = NextResponse.json({
        hasConsent: false,
        message: 'Consent removed from database, clearing local cookie',
        isAnonymous: !userInfo,
        source: 'database_cleared'
      })
      
      // Clear the local consent cookie
      response.cookies.set({
        name: CONSENT_COOKIE_NAME,
        value: '',
        maxAge: 0,
        path: '/',
        sameSite: 'strict',
        secure: process.env.NODE_ENV === 'production'
      })
      
      console.log('🍪 Local consent cookie cleared')
      return response
    }

    // No consent found anywhere
    console.log('❌ No consent found in database or local cookies')
    return NextResponse.json({
      hasConsent: false,
      message: 'No consent found',
      isAnonymous: !userInfo,
      source: 'none'
    })

  } catch (error) {
    console.error('❌ Cookie consent API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('🍪 === COOKIE CONSENT POST REQUEST START ===')
    
    const body = await request.json()
    const { preferences, timestamp, consentMethod } = body
    
    console.log('🍪 Request body:', { preferences, timestamp, consentMethod })

    // Validate preferences
    if (!preferences || typeof preferences !== 'object') {
      console.error('❌ Invalid preferences data:', preferences)
      return NextResponse.json(
        { error: 'Invalid preferences data' },
        { status: 400 }
      )
    }

    // Ensure essential cookies are always enabled
    const validatedPreferences: CookiePreferences = {
      essential: true, // Always true
      analytics: Boolean(preferences.analytics),
      marketing: Boolean(preferences.marketing),
      functional: Boolean(preferences.functional)
    }
    
    console.log('🍪 Validated preferences:', validatedPreferences)

    // Get user information and anonymous ID
    const userAgent = request.headers.get('user-agent') || ''
    const forwardedFor = request.headers.get('x-forwarded-for')
    const realIp = request.headers.get('x-real-ip')
    const ipAddress = forwardedFor?.split(',')[0] || realIp || 'unknown'
    
    const userInfo = await getUserInfoFromCookie()
    const anonymousId = await getOrCreateAnonymousId()
    
    console.log('🍪 User info:', userInfo ? { email: userInfo.email, firstName: userInfo.firstName, lastName: userInfo.lastName } : 'Anonymous')
    console.log('🍪 Anonymous ID:', anonymousId)

    // Create consent data
    const consentData: ConsentData = {
      preferences: validatedPreferences,
      timestamp: timestamp || new Date().toISOString(),
      userAgent,
      ipAddress: ipAddress.substring(0, 8) + '...', // Partial IP for privacy
      anonymousId
    }

    // Set anonymous ID cookie if not exists
    const cookieStore = await cookies()
    const existingAnonymousId = cookieStore.get(ANONYMOUS_ID_COOKIE_NAME)
    
    // Save to database first (both logged-in and anonymous users)
    let dbSaveSuccess = false
    let dbResult = null

    console.log('🍪 === STARTING DATABASE SAVE ===')
    console.log('🍪 Admin API URL:', ADMIN_API_URL)

    if (userInfo) {
      try {
        console.log('🍪 Saving consent for logged-in user:', userInfo.email)
        
        const requestPayload = {
          firstName: userInfo.firstName,
          lastName: userInfo.lastName,
          email: userInfo.email,
          preferences: validatedPreferences,
          consentMethod: consentMethod || 'banner_custom',
          ipAddress: ipAddress,
          userAgent: userAgent,
          anonymousId: anonymousId
        }
        console.log('🍪 Request payload:', requestPayload)

        const dbResponse = await fetch(`${ADMIN_API_URL}/api/public/cookie-consent`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestPayload)
        })

        console.log('🍪 Admin API response status:', dbResponse.status)
        console.log('🍪 Admin API response headers:', Object.fromEntries(dbResponse.headers.entries()))

        if (dbResponse.ok) {
          dbResult = await dbResponse.json()
          dbSaveSuccess = true
          console.log('✅ Cookie consent saved to database for logged-in user:', dbResult)
        } else {
          const errorText = await dbResponse.text()
          console.error('❌ Failed to save consent to database:', {
            status: dbResponse.status,
            statusText: dbResponse.statusText,
            errorText: errorText
          })
        }
      } catch (dbError) {
        console.error('❌ Database save failed for logged-in user:', {
          error: dbError,
          message: dbError.message,
          stack: dbError.stack
        })
      }
    } else {
      // For anonymous users, save with anonymous data
      try {
        console.log('🍪 Saving consent for anonymous user:', anonymousId)
        
        const requestPayload = {
          firstName: 'Anonymous',
          lastName: 'User',
          email: `anonymous_${anonymousId}@temp.local`,
          preferences: validatedPreferences,
          consentMethod: consentMethod || 'banner_custom',
          ipAddress: ipAddress,
          userAgent: userAgent,
          anonymousId: anonymousId
        }
        console.log('🍪 Request payload:', requestPayload)

        const dbResponse = await fetch(`${ADMIN_API_URL}/api/public/cookie-consent`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestPayload)
        })

        console.log('🍪 Admin API response status:', dbResponse.status)
        console.log('🍪 Admin API response headers:', Object.fromEntries(dbResponse.headers.entries()))

        if (dbResponse.ok) {
          dbResult = await dbResponse.json()
          dbSaveSuccess = true
          console.log('✅ Cookie consent saved to database for anonymous user:', dbResult)
        } else {
          const errorText = await dbResponse.text()
          console.error('❌ Failed to save anonymous consent to database:', {
            status: dbResponse.status,
            statusText: dbResponse.statusText,
            errorText: errorText
          })
        }
      } catch (dbError) {
        console.error('❌ Anonymous database save failed:', {
          error: dbError,
          message: dbError.message,
          stack: dbError.stack
        })
      }
    }

    console.log('🍪 Database save result:', { dbSaveSuccess, dbResult })

    // Only set local cookies if database save was successful
    if (dbSaveSuccess) {
      // Create response
      const response = NextResponse.json({
        success: true,
        preferences: validatedPreferences,
        timestamp: consentData.timestamp,
        isAnonymous: !userInfo,
        dbSaved: dbSaveSuccess,
        dbResult: dbResult
      })

      console.log('🍪 === SETTING COOKIES ===')

      // Set anonymous ID cookie if not exists
      if (!existingAnonymousId) {
        console.log('🍪 Setting anonymous ID cookie:', anonymousId)
        response.cookies.set({
          name: ANONYMOUS_ID_COOKIE_NAME,
          value: anonymousId,
          maxAge: 365 * 24 * 60 * 60, // 1 year
          path: '/',
          sameSite: 'strict',
          secure: process.env.NODE_ENV === 'production',
          httpOnly: true // Secure anonymous ID
        })
      }

      // Set consent cookie
      try {
        const cookieValue = encodeURIComponent(JSON.stringify(consentData))
        console.log('🍪 Setting consent cookie, length:', cookieValue.length)
        
        // Check if cookie value is too large (browsers typically limit to 4KB)
        if (cookieValue.length > 4000) {
          console.warn('Cookie value too large, using simplified format')
          // Use simplified format for large cookies
          const simplifiedData = {
            preferences: validatedPreferences,
            timestamp: consentData.timestamp,
            anonymousId
          }
          const simplifiedValue = encodeURIComponent(JSON.stringify(simplifiedData))
          
          response.cookies.set({
            name: CONSENT_COOKIE_NAME,
            value: simplifiedValue,
            maxAge: 365 * 24 * 60 * 60, // 1 year
            path: '/',
            sameSite: 'strict',
            secure: process.env.NODE_ENV === 'production',
            httpOnly: false // Allow client-side access for preference checking
          })
        } else {
          response.cookies.set({
            name: CONSENT_COOKIE_NAME,
            value: cookieValue,
            maxAge: 365 * 24 * 60 * 60, // 1 year
            path: '/',
            sameSite: 'strict',
            secure: process.env.NODE_ENV === 'production',
            httpOnly: false // Allow client-side access for preference checking
          })
        }
        console.log('✅ Consent cookie set successfully')
      } catch (cookieError) {
        console.error('❌ Error setting cookie:', cookieError)
      }

      console.log('🍪 === COOKIE CONSENT POST REQUEST END ===')
      return response
    } else {
      // Database save failed, don't set local cookies
      console.error('❌ Database save failed, not setting local cookies')
      return NextResponse.json({
        success: false,
        error: 'Failed to save consent to database',
        preferences: validatedPreferences,
        timestamp: consentData.timestamp,
        isAnonymous: !userInfo,
        dbSaved: false,
        dbResult: dbResult
      }, { status: 500 })
    }
  } catch (error) {
    console.error('❌ Cookie consent API error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const userInfo = await getUserInfoFromCookie()
    const anonymousId = await getOrCreateAnonymousId()

    // Clear from database
    if (userInfo) {
      // Clear for logged-in user
      try {
        const response = await fetch(`${ADMIN_API_URL}/api/public/cookie-consent`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: userInfo.email,
            reason: 'user_requested'
          })
        })

        if (response.ok) {
          console.log('🍪 Cookie consent cleared from database for logged-in user')
        } else {
          console.error('Failed to clear consent from database:', response.status)
        }
      } catch (dbError) {
        console.error('Database clear failed:', dbError)
      }
    } else {
      // Clear for anonymous user
      try {
        const response = await fetch(`${ADMIN_API_URL}/api/public/cookie-consent`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: `anonymous_${anonymousId}@temp.local`,
            reason: 'user_requested'
          })
        })

        if (response.ok) {
          console.log('🍪 Cookie consent cleared from database for anonymous user')
        } else {
          console.error('Failed to clear anonymous consent from database:', response.status)
        }
      } catch (dbError) {
        console.error('Anonymous database clear failed:', dbError)
      }
    }

    const response = NextResponse.json({ 
      success: true,
      message: 'Consent cleared'
    })

    // Clear consent cookie
    response.cookies.set({
      name: CONSENT_COOKIE_NAME,
      value: '',
      maxAge: 0,
      path: '/',
      sameSite: 'strict',
      secure: process.env.NODE_ENV === 'production'
    })

    return response
  } catch (error) {
    console.error('Cookie consent API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}