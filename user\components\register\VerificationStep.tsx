"use client";

import React from "react";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { InputOTP, InputOTPGroup, InputOTPSlot } from "@/components/ui/input-otp";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, CheckCircle2 } from "lucide-react";

interface VerificationStepProps {
  verificationStep: "email";
  verificationCode: string;
  setVerificationCode: (code: string) => void;
  handleVerificationSubmit: (e: React.FormEvent) => Promise<void>;
  isLoading: boolean;
  error: string | null;
  email: string; // To display which email is being verified
  phone: string; // Kept for compatibility
  countryCode: string; // Kept for compatibility
}

const VerificationStep: React.FC<VerificationStepProps> = ({
  verificationStep,
  verificationCode,
  setVerificationCode,
  handleVerificationSubmit,
  isLoading,
  error,
  email,
  phone,
  countryCode
}) => {
  const target = email;

  return (
    <motion.form
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      onSubmit={handleVerificationSubmit}
      className="space-y-8 w-full max-w-md mx-auto p-8 bg-white border rounded-xl shadow-sm overflow-hidden"
    >
      <div className="text-center space-y-2">
        <div className="inline-block p-4 rounded-full bg-teal-100 text-teal-600 mb-3">
          <CheckCircle2 className="h-8 w-8" />
        </div>
        <h2 className="text-2xl font-semibold tracking-tight">
          Verify Your Email
        </h2>
        <p className="text-gray-600">
          We sent a verification code to <strong>{target}</strong>. Please enter the code below.
        </p>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="flex flex-col items-center space-y-4">
        <Label htmlFor="otp-code" className="sr-only">
          Verification Code
        </Label>
        <InputOTP
          id="otp-code"
          maxLength={6}
          value={verificationCode}
          onChange={setVerificationCode}
          disabled={isLoading}
          containerClassName="gap-6"
        >
          <InputOTPGroup>
            <InputOTPSlot index={0} />
            <InputOTPSlot index={1} />
            <InputOTPSlot index={2} />
            <InputOTPSlot index={3} />
            <InputOTPSlot index={4} />
            <InputOTPSlot index={5} />
          </InputOTPGroup>
        </InputOTP>

        <Button type="submit" disabled={isLoading || verificationCode.length < 6} className="w-full bg-teal-600 hover:bg-teal-700">
          {isLoading ? (
             <svg className="animate-spin h-5 w-5 mr-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
               <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
               <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
             </svg>
          ) : null}
          {isLoading ? "Verifying..." : "Verify Code"}
        </Button>
      </div>

      {/* Optional: Add a resend code button here if needed */}
      {/* <div className="text-center">
        <Button variant="link" type="button" disabled={isLoading}>Resend Code</Button>
      </div> */}
    </motion.form>
  );
};

export default VerificationStep;

